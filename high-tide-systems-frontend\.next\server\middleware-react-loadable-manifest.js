self.__REACT_LOADABLE_MANIFEST="{\"..\\\\node_modules\\\\jspdf\\\\dist\\\\jspdf.es.min.js -> canvg\":{\"id\":\"..\\\\node_modules\\\\jspdf\\\\dist\\\\jspdf.es.min.js -> canvg\",\"files\":[\"static/chunks/_app-pages-browser_node_modules_canvg_lib_index_es_js.js\"]},\"..\\\\node_modules\\\\jspdf\\\\dist\\\\jspdf.es.min.js -> dompurify\":{\"id\":\"..\\\\node_modules\\\\jspdf\\\\dist\\\\jspdf.es.min.js -> dompurify\",\"files\":[\"static/chunks/_app-pages-browser_node_modules_dompurify_dist_purify_es_mjs.js\"]},\"..\\\\node_modules\\\\jspdf\\\\dist\\\\jspdf.es.min.js -> html2canvas\":{\"id\":\"..\\\\node_modules\\\\jspdf\\\\dist\\\\jspdf.es.min.js -> html2canvas\",\"files\":[]},\"..\\\\node_modules\\\\next\\\\dist\\\\client\\\\components\\\\react-dev-overlay\\\\internal\\\\helpers\\\\use-websocket.js -> @vercel/turbopack-ecmascript-runtime/browser/dev/hmr-client/hmr-client.ts\":{\"id\":\"..\\\\node_modules\\\\next\\\\dist\\\\client\\\\components\\\\react-dev-overlay\\\\internal\\\\helpers\\\\use-websocket.js -> @vercel/turbopack-ecmascript-runtime/browser/dev/hmr-client/hmr-client.ts\",\"files\":[\"static/chunks/_app-pages-browser_node_modules_next_dist_client_dev_noop-turbopack-hmr_js.js\"]},\"app\\\\modules\\\\scheduler\\\\calendar\\\\hooks\\\\useAppointmentCalendar.js -> socket.io-client\":{\"id\":\"app\\\\modules\\\\scheduler\\\\calendar\\\\hooks\\\\useAppointmentCalendar.js -> socket.io-client\",\"files\":[\"static/chunks/_app-pages-browser_node_modules_socket_io-client_build_esm_index_js.js\"]},\"app\\\\modules\\\\scheduler\\\\services\\\\appointmentDashboardService.js -> @/utils/api\":{\"id\":\"app\\\\modules\\\\scheduler\\\\services\\\\appointmentDashboardService.js -> @/utils/api\",\"files\":[]},\"components\\\\calendar\\\\AppointmentModal.js -> @/app/modules/scheduler/services/serviceTypeService\":{\"id\":\"components\\\\calendar\\\\AppointmentModal.js -> @/app/modules/scheduler/services/serviceTypeService\",\"files\":[\"static/chunks/_app-pages-browser_src_app_modules_scheduler_services_serviceTypeService_js.js\"]}}"