"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/scheduler/working-hours/page",{

/***/ "(app-pages-browser)/./src/components/calendar/AppointmentModal.js":
/*!*****************************************************!*\
  !*** ./src/components/calendar/AppointmentModal.js ***!
  \*****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AppointmentModal: () => (/* binding */ AppointmentModal),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _contexts_ToastContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/ToastContext */ \"(app-pages-browser)/./src/contexts/ToastContext.js\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.js\");\n/* harmony import */ var _barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=format!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/format.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _components_ui_ModuleModal__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/ModuleModal */ \"(app-pages-browser)/./src/components/ui/ModuleModal.js\");\n/* harmony import */ var _app_modules_scheduler_services_appointmentService__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/app/modules/scheduler/services/appointmentService */ \"(app-pages-browser)/./src/app/modules/scheduler/services/appointmentService.js\");\n/* harmony import */ var _app_modules_scheduler_services_insuranceLimitService__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/app/modules/scheduler/services/insuranceLimitService */ \"(app-pages-browser)/./src/app/modules/scheduler/services/insuranceLimitService.js\");\n/* harmony import */ var _app_modules_scheduler_services_insuranceServiceLimitService__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/app/modules/scheduler/services/insuranceServiceLimitService */ \"(app-pages-browser)/./src/app/modules/scheduler/services/insuranceServiceLimitService.js\");\n/* harmony import */ var _app_modules_people_services_insurancesService__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/app/modules/people/services/insurancesService */ \"(app-pages-browser)/./src/app/modules/people/services/insurancesService.js\");\n/* harmony import */ var _components_ui_ConfirmationDialog__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/ConfirmationDialog */ \"(app-pages-browser)/./src/components/ui/ConfirmationDialog.js\");\n/* harmony import */ var _appointmentModal_BasicInfoForm__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./appointmentModal/BasicInfoForm */ \"(app-pages-browser)/./src/components/calendar/appointmentModal/BasicInfoForm.js\");\n/* harmony import */ var _appointmentModal_SpecialtySelector__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./appointmentModal/SpecialtySelector */ \"(app-pages-browser)/./src/components/calendar/appointmentModal/SpecialtySelector.js\");\n/* harmony import */ var _appointmentModal_AppointmentSelectors__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./appointmentModal/AppointmentSelectors */ \"(app-pages-browser)/./src/components/calendar/appointmentModal/AppointmentSelectors.js\");\n/* harmony import */ var _InsuranceLimitsDisplay__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ./InsuranceLimitsDisplay */ \"(app-pages-browser)/./src/components/calendar/InsuranceLimitsDisplay.js\");\n/* harmony import */ var _appointmentModal_AppointmentDates__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! ./appointmentModal/AppointmentDates */ \"(app-pages-browser)/./src/components/calendar/appointmentModal/AppointmentDates.js\");\n/* harmony import */ var _appointmentModal_AppointmentStatus__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! ./appointmentModal/AppointmentStatus */ \"(app-pages-browser)/./src/components/calendar/appointmentModal/AppointmentStatus.js\");\n/* harmony import */ var _appointmentModal_SequentialAppointments__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! ./appointmentModal/SequentialAppointments */ \"(app-pages-browser)/./src/components/calendar/appointmentModal/SequentialAppointments.js\");\n/* harmony import */ var _appointmentModal_RecurrenceSettings__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! ./appointmentModal/RecurrenceSettings */ \"(app-pages-browser)/./src/components/calendar/appointmentModal/RecurrenceSettings.js\");\n/* harmony import */ var _appointmentModal_ModuleModalFooter__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! ./appointmentModal/ModuleModalFooter */ \"(app-pages-browser)/./src/components/calendar/appointmentModal/ModuleModalFooter.js\");\n/* __next_internal_client_entry_do_not_use__ AppointmentModal,default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n// import { utcToLocal, localToUTC } from \"@/utils/dateFormatters\";\n\n\n\n\n\n\n\n// Importando os componentes refatorados\n\n\n\n\n\n\n\n\n\nconst AppointmentModal = (param)=>{\n    let { isOpen, onClose, selectedDate, selectedAppointment, onAppointmentChange, checkAvailability, canCreate = false, canEdit = false, canDelete = false } = param;\n    _s();\n    // Função para forçar o fechamento do modal\n    const forceCloseModal = ()=>{\n        // Resetar TODOS os estados que podem estar impedindo o fechamento\n        setConfirmationDialogOpen(false);\n        // Removido setCanCloseMainModal pois não existe mais\n        setIsLoading(false);\n        // Chamar onClose diretamente\n        onClose();\n    };\n    const { toast_success, toast_error } = (0,_contexts_ToastContext__WEBPACK_IMPORTED_MODULE_2__.useToast)();\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        title: \"\",\n        description: \"\",\n        specialty: \"\",\n        providerId: \"\",\n        personId: \"\",\n        locationId: \"\",\n        serviceTypeId: \"\",\n        insuranceId: \"\",\n        startDate: null,\n        endDate: null,\n        sequentialAppointments: 1,\n        status: \"PENDING\",\n        recurrence: {\n            enabled: false,\n            type: \"OCCURRENCES\",\n            numberOfOccurrences: 1,\n            endDate: null,\n            patterns: []\n        }\n    });\n    const [providers, setProviders] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [filteredProviders, setFilteredProviders] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [specialties, setSpecialties] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [persons, setPersons] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [locations, setLocations] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [serviceTypes, setServiceTypes] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [personInsurances, setPersonInsurances] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoadingInsurances, setIsLoadingInsurances] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isCheckingLimits, setIsCheckingLimits] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [limitInfo, setLimitInfo] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [confirmationDialogOpen, setConfirmationDialogOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [actionToConfirm, setActionToConfirm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Removido canCloseMainModal pois não é mais necessário\n    // Efeito para pré-carregar dados quando o modal é aberto com um agendamento existente\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AppointmentModal.useEffect\": ()=>{\n            if (isOpen && selectedAppointment) {\n                console.log(\"Modal aberto com agendamento existente, pré-carregando dados\");\n                console.log(\"Dados completos do agendamento selecionado:\", selectedAppointment);\n                // Verificar se o ID está definido\n                if (!selectedAppointment.id || selectedAppointment.id === \"undefined\") {\n                    console.error(\"ERRO: ID do agendamento inválido!\", selectedAppointment.id);\n                    return;\n                }\n                // Verificar se temos o personId\n                if (!selectedAppointment.personId) {\n                    console.warn(\"ALERTA: personId não encontrado no agendamento selecionado!\");\n                    // Não temos personId, mas não vamos buscar da API novamente\n                    // Apenas exibir um aviso e continuar com os dados que temos\n                    console.log(\"Continuando com os dados disponíveis sem personId\");\n                }\n                // Carregar convênios para o paciente se tivermos personId\n                if (selectedAppointment.personId) {\n                    loadPersonInsurances(selectedAppointment.personId, selectedAppointment.insuranceId);\n                    // Carregar tipos de serviço se tivermos convênio\n                    if (selectedAppointment.insuranceId) {\n                        loadServiceTypes(selectedAppointment.personId, selectedAppointment.insuranceId, selectedAppointment.serviceTypeId);\n                    }\n                }\n            }\n        }\n    }[\"AppointmentModal.useEffect\"], [\n        isOpen,\n        selectedAppointment\n    ]);\n    // Função para carregar convênios do paciente\n    const loadPersonInsurances = (personId, insuranceId)=>{\n        if (!personId) return;\n        console.log(\"Carregando conv\\xeanios para o paciente ID: \".concat(personId));\n        setIsLoadingInsurances(true);\n        _app_modules_people_services_insurancesService__WEBPACK_IMPORTED_MODULE_8__[\"default\"].listPersonInsurances(personId).then((insurances)=>{\n            console.log(\"Pr\\xe9-carregados \".concat((insurances === null || insurances === void 0 ? void 0 : insurances.length) || 0, \" conv\\xeanios para o paciente \").concat(personId));\n            setPersonInsurances(insurances || []);\n            // Verificar se o convênio do agendamento está na lista\n            if (insuranceId && insurances && insurances.length > 0) {\n                const insuranceExists = insurances.some((ins)=>(ins.insuranceId || ins.id) === insuranceId);\n                if (!insuranceExists) {\n                    console.log(\"Conv\\xeanio \".concat(insuranceId, \" n\\xe3o encontrado na lista, adicionando manualmente\"));\n                    // Adicionar o convênio manualmente à lista\n                    if (selectedAppointment.insurance) {\n                        const manualInsurance = {\n                            id: insuranceId,\n                            insuranceId: insuranceId,\n                            name: selectedAppointment.insurance.name || \"Convênio\",\n                            insurance: selectedAppointment.insurance\n                        };\n                        setPersonInsurances((prev)=>[\n                                ...prev,\n                                manualInsurance\n                            ]);\n                    } else {\n                        // Tentar buscar o convênio do backend\n                        _app_modules_people_services_insurancesService__WEBPACK_IMPORTED_MODULE_8__[\"default\"].getInsuranceById(insuranceId).then((insuranceData)=>{\n                            if (insuranceData) {\n                                const manualInsurance = {\n                                    id: insuranceId,\n                                    insuranceId: insuranceId,\n                                    name: insuranceData.name || \"Convênio\",\n                                    insurance: insuranceData\n                                };\n                                setPersonInsurances((prev)=>[\n                                        ...prev,\n                                        manualInsurance\n                                    ]);\n                            }\n                        }).catch((error)=>{\n                            console.error(\"Erro ao buscar convênio do backend:\", error);\n                        });\n                    }\n                }\n            }\n        }).catch((error)=>{\n            console.error(\"Erro ao pré-carregar convênios:\", error);\n        }).finally(()=>{\n            setIsLoadingInsurances(false);\n        });\n    };\n    // Função para carregar tipos de serviço\n    const loadServiceTypes = (personId, insuranceId, serviceTypeId)=>{\n        if (!personId || !insuranceId) return;\n        console.log(\"Carregando tipos de servi\\xe7o para o paciente ID: \".concat(personId, \" e conv\\xeanio ID: \").concat(insuranceId));\n        setIsLoading(true);\n        _app_modules_scheduler_services_insuranceServiceLimitService__WEBPACK_IMPORTED_MODULE_7__[\"default\"].getServiceTypesByInsurance(insuranceId, personId).then((serviceTypes)=>{\n            console.log(\"Pr\\xe9-carregados \".concat((serviceTypes === null || serviceTypes === void 0 ? void 0 : serviceTypes.length) || 0, \" tipos de servi\\xe7o\"));\n            setServiceTypes(serviceTypes || []);\n            // Verificar se o tipo de serviço do agendamento está na lista\n            if (serviceTypeId && serviceTypes && serviceTypes.length > 0) {\n                const serviceTypeExists = serviceTypes.some((type)=>type.id === serviceTypeId);\n                if (!serviceTypeExists) {\n                    console.log(\"Tipo de servi\\xe7o \".concat(serviceTypeId, \" n\\xe3o encontrado na lista, adicionando manualmente\"));\n                    // Adicionar o tipo de serviço manualmente à lista\n                    if (selectedAppointment.serviceType) {\n                        const manualServiceType = {\n                            id: serviceTypeId,\n                            name: selectedAppointment.serviceType.name || \"Tipo de Serviço\",\n                            value: selectedAppointment.serviceType.value || \"0\"\n                        };\n                        setServiceTypes((prev)=>[\n                                ...prev,\n                                manualServiceType\n                            ]);\n                    } else {\n                        // Tentar buscar o tipo de serviço do backend\n                        _app_modules_scheduler_services_insuranceServiceLimitService__WEBPACK_IMPORTED_MODULE_7__[\"default\"].getServiceTypeById(serviceTypeId).then((serviceTypeData)=>{\n                            if (serviceTypeData) {\n                                const manualServiceType = {\n                                    id: serviceTypeId,\n                                    name: serviceTypeData.name || \"Tipo de Serviço\",\n                                    value: serviceTypeData.value || \"0\"\n                                };\n                                setServiceTypes((prev)=>[\n                                        ...prev,\n                                        manualServiceType\n                                    ]);\n                            }\n                        }).catch((error)=>{\n                            console.error(\"Erro ao buscar tipo de serviço do backend:\", error);\n                        });\n                    }\n                }\n            }\n        }).catch((error)=>{\n            console.error(\"Erro ao pré-carregar tipos de serviço:\", error);\n        }).finally(()=>{\n            setIsLoading(false);\n        });\n    };\n    // Garantir que o modal possa ser fechado quando isOpen mudar para false\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AppointmentModal.useEffect\": ()=>{\n            if (!isOpen) {\n                // Resetar TODOS os estados que podem estar impedindo o fechamento\n                setConfirmationDialogOpen(false);\n                setIsLoading(false);\n                // Limpar formulário\n                setFormData({\n                    title: \"\",\n                    description: \"\",\n                    specialty: \"\",\n                    providerId: \"\",\n                    personId: \"\",\n                    locationId: \"\",\n                    serviceTypeId: \"\",\n                    insuranceId: \"\",\n                    startDate: null,\n                    endDate: null,\n                    sequentialAppointments: 1,\n                    status: \"PENDING\",\n                    recurrence: {\n                        enabled: false,\n                        type: \"OCCURRENCES\",\n                        numberOfOccurrences: 1,\n                        endDate: null,\n                        patterns: []\n                    }\n                });\n            }\n        }\n    }[\"AppointmentModal.useEffect\"], [\n        isOpen\n    ]);\n    // Verificar se o modal deve exibir modo de edição ou criação\n    const isEditMode = !!selectedAppointment;\n    // Verificar se o usuário tem permissão para a ação atual\n    const hasPermission = isEditMode ? canEdit : canCreate;\n    // Verificar se o usuário é um cliente\n    const { user } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    const isClient = user === null || user === void 0 ? void 0 : user.isClient;\n    // Modo somente leitura para clientes\n    const isReadOnlyMode = isClient && isEditMode;\n    // Se não tiver permissão e não for cliente em modo somente leitura, exibir mensagem\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AppointmentModal.useEffect\": ()=>{\n            if (isOpen && !hasPermission && !isReadOnlyMode) {\n                toast_error(isEditMode ? \"Você não tem permissão para editar agendamentos.\" : \"Você não tem permissão para criar agendamentos.\");\n            }\n        }\n    }[\"AppointmentModal.useEffect\"], [\n        isOpen,\n        hasPermission,\n        isEditMode,\n        isReadOnlyMode,\n        toast_error\n    ]);\n    // Função para extrair especialidades únicas dos provedores\n    const extractSpecialties = (providersList)=>{\n        const uniqueSpecialties = [\n            ...new Set(providersList.filter((provider)=>{\n                var _provider_professionObj;\n                return provider && (provider.profession || ((_provider_professionObj = provider.professionObj) === null || _provider_professionObj === void 0 ? void 0 : _provider_professionObj.name));\n            }).map((provider)=>{\n                var _provider_professionObj;\n                return provider.profession || ((_provider_professionObj = provider.professionObj) === null || _provider_professionObj === void 0 ? void 0 : _provider_professionObj.name);\n            }))\n        ].sort();\n        return uniqueSpecialties;\n    };\n    // Filtrar provedores baseado na especialidade selecionada\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AppointmentModal.useEffect\": ()=>{\n            if (formData.specialty) {\n                const filtered = providers.filter({\n                    \"AppointmentModal.useEffect.filtered\": (provider)=>{\n                        var _provider_professionObj;\n                        const providerProfession = provider.profession || ((_provider_professionObj = provider.professionObj) === null || _provider_professionObj === void 0 ? void 0 : _provider_professionObj.name);\n                        return providerProfession === formData.specialty;\n                    }\n                }[\"AppointmentModal.useEffect.filtered\"]);\n                setFilteredProviders(filtered);\n                // Se o provedor atualmente selecionado não estiver nesta especialidade, limpe a seleção\n                if (formData.providerId && !filtered.some({\n                    \"AppointmentModal.useEffect\": (p)=>p.id === formData.providerId\n                }[\"AppointmentModal.useEffect\"])) {\n                    setFormData({\n                        \"AppointmentModal.useEffect\": (prev)=>({\n                                ...prev,\n                                providerId: \"\"\n                            })\n                    }[\"AppointmentModal.useEffect\"]);\n                }\n            } else {\n                setFilteredProviders(providers);\n            }\n        }\n    }[\"AppointmentModal.useEffect\"], [\n        formData.specialty,\n        providers,\n        formData.providerId\n    ]);\n    // Carregar dados para os selects\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AppointmentModal.useEffect\": ()=>{\n            const loadData = {\n                \"AppointmentModal.useEffect.loadData\": async ()=>{\n                    setIsLoading(true);\n                    try {\n                        console.log(\"Loading appointment modal data...\");\n                        // Load data separately to better debug any issues\n                        console.log(\"Loading providers...\");\n                        const providersRes = await _app_modules_scheduler_services_appointmentService__WEBPACK_IMPORTED_MODULE_5__.appointmentService.getProviders();\n                        console.log(\"Loaded \".concat((providersRes === null || providersRes === void 0 ? void 0 : providersRes.length) || 0, \" providers\"));\n                        console.log(\"Loading persons...\");\n                        const personsRes = await _app_modules_scheduler_services_appointmentService__WEBPACK_IMPORTED_MODULE_5__.appointmentService.getPersons();\n                        console.log(\"Loaded \".concat((personsRes === null || personsRes === void 0 ? void 0 : personsRes.length) || 0, \" persons\"));\n                        console.log(\"Loading locations...\");\n                        const locationsRes = await _app_modules_scheduler_services_appointmentService__WEBPACK_IMPORTED_MODULE_5__.appointmentService.getLocations();\n                        console.log(\"Loaded \".concat((locationsRes === null || locationsRes === void 0 ? void 0 : locationsRes.length) || 0, \" locations\"));\n                        // Set state with valid data only\n                        const validProviders = providersRes || [];\n                        setProviders(validProviders);\n                        setFilteredProviders(validProviders);\n                        setPersons(personsRes || []);\n                        // Extract and set unique specialties\n                        const extractedSpecialties = extractSpecialties(validProviders);\n                        setSpecialties(extractedSpecialties);\n                        setLocations(locationsRes || []);\n                        // Log what was set\n                        console.log(\"Set providers:\", (validProviders === null || validProviders === void 0 ? void 0 : validProviders.length) || 0);\n                        console.log(\"Set specialties:\", (extractedSpecialties === null || extractedSpecialties === void 0 ? void 0 : extractedSpecialties.length) || 0);\n                        console.log(\"Set persons:\", (personsRes === null || personsRes === void 0 ? void 0 : personsRes.length) || 0);\n                        console.log(\"Set locations:\", (locationsRes === null || locationsRes === void 0 ? void 0 : locationsRes.length) || 0);\n                    } catch (error) {\n                        console.error(\"Erro ao carregar dados:\", error);\n                        toast_error(\"Erro ao carregar dados necessários. Por favor, tente novamente.\");\n                    } finally{\n                        setIsLoading(false);\n                    }\n                }\n            }[\"AppointmentModal.useEffect.loadData\"];\n            if (isOpen) {\n                loadData();\n            }\n        }\n    }[\"AppointmentModal.useEffect\"], [\n        isOpen\n    ]);\n    // Carregar os convênios do paciente selecionado\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AppointmentModal.useEffect\": ()=>{\n            const loadPersonInsurances = {\n                \"AppointmentModal.useEffect.loadPersonInsurances\": async ()=>{\n                    // Se não tiver um personId (paciente) selecionado, não fazer nada\n                    if (!formData.personId) {\n                        setPersonInsurances([]);\n                        return;\n                    }\n                    setIsLoadingInsurances(true);\n                    try {\n                        console.log(\"Carregando conv\\xeanios do paciente ID: \".concat(formData.personId));\n                        const insurancesRes = await _app_modules_people_services_insurancesService__WEBPACK_IMPORTED_MODULE_8__[\"default\"].listPersonInsurances(formData.personId);\n                        if (insurancesRes && insurancesRes.length > 0) {\n                            console.log(\"Carregados \".concat(insurancesRes.length, \" conv\\xeanios do paciente\"));\n                            console.log(\"Primeiro convênio:\", insurancesRes[0]);\n                            setPersonInsurances(insurancesRes);\n                            // Se estamos editando um agendamento existente e o convênio não está na lista,\n                            // não limpar o convênio selecionado\n                            if (formData.id && formData.insuranceId) {\n                                const insuranceExists = insurancesRes.some({\n                                    \"AppointmentModal.useEffect.loadPersonInsurances.insuranceExists\": (ins)=>(ins.insuranceId || ins.id) === formData.insuranceId\n                                }[\"AppointmentModal.useEffect.loadPersonInsurances.insuranceExists\"]);\n                                if (!insuranceExists) {\n                                    console.log(\"Conv\\xeanio \".concat(formData.insuranceId, \" n\\xe3o encontrado na lista, mas mantendo-o selecionado para edi\\xe7\\xe3o\"));\n                                }\n                            }\n                        // Removido o reset do convênio para novos agendamentos\n                        } else {\n                            console.warn(\"Nenhum convênio encontrado para este paciente\");\n                        // Manter o convênio selecionado mesmo se não houver convênios disponíveis\n                        // Isso permite que o usuário selecione \"Sem convênio\" manualmente\n                        }\n                    } catch (error) {\n                        console.error(\"Erro ao carregar conv\\xeanios do paciente \".concat(formData.personId, \":\"), error);\n                    } finally{\n                        setIsLoadingInsurances(false);\n                    }\n                }\n            }[\"AppointmentModal.useEffect.loadPersonInsurances\"];\n            // Carregar os convênios sempre que o paciente mudar\n            loadPersonInsurances();\n        }\n    }[\"AppointmentModal.useEffect\"], [\n        formData.personId,\n        formData.id,\n        formData.insuranceId\n    ]);\n    // Preencher dados do formulário baseado no evento selecionado ou data\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AppointmentModal.useEffect\": ()=>{\n            if (selectedAppointment) {\n                var _provider_professionObj;\n                console.log(\"Selected appointment:\", selectedAppointment);\n                // Verificar se o ID está definido\n                if (!selectedAppointment.id) {\n                    console.error(\"ERRO: ID do agendamento não definido!\");\n                    return;\n                }\n                // Buscar especialidade do provedor selecionado\n                const provider = providers.find({\n                    \"AppointmentModal.useEffect.provider\": (p)=>p.id === selectedAppointment.providerId\n                }[\"AppointmentModal.useEffect.provider\"]);\n                const providerSpecialty = (provider === null || provider === void 0 ? void 0 : provider.profession) || (provider === null || provider === void 0 ? void 0 : (_provider_professionObj = provider.professionObj) === null || _provider_professionObj === void 0 ? void 0 : _provider_professionObj.name) || \"\";\n                // Edição de agendamento existente\n                // Usar setTimeout para garantir que este efeito seja executado após os outros efeitos\n                // que podem estar tentando limpar os dados\n                setTimeout({\n                    \"AppointmentModal.useEffect\": ()=>{\n                        console.log(\"Preenchendo formulário com dados do agendamento selecionado\");\n                        console.log(\"Dados do agendamento:\", {\n                            id: selectedAppointment.id,\n                            personId: selectedAppointment.personId,\n                            insuranceId: selectedAppointment.insuranceId,\n                            serviceTypeId: selectedAppointment.serviceTypeId\n                        });\n                        // Garantir que temos valores válidos para todos os campos\n                        const formDataValues = {\n                            id: selectedAppointment.id,\n                            title: selectedAppointment.title || \"\",\n                            description: selectedAppointment.description || \"\",\n                            specialty: providerSpecialty || \"\",\n                            providerId: selectedAppointment.providerId || \"\",\n                            personId: selectedAppointment.personId || \"\",\n                            locationId: selectedAppointment.locationId || \"\",\n                            serviceTypeId: selectedAppointment.serviceTypeId || \"\",\n                            insuranceId: selectedAppointment.insuranceId || \"\",\n                            startDate: selectedAppointment.startDate || new Date().toISOString(),\n                            endDate: selectedAppointment.endDate || new Date(Date.now() + 3600000).toISOString(),\n                            sequentialAppointments: 1,\n                            status: selectedAppointment.status || \"PENDING\",\n                            recurrence: {\n                                enabled: false,\n                                type: \"OCCURRENCES\",\n                                numberOfOccurrences: 1,\n                                endDate: null,\n                                patterns: []\n                            }\n                        };\n                        console.log(\"Valores finais do formulário:\", formDataValues);\n                        setFormData(formDataValues);\n                    }\n                }[\"AppointmentModal.useEffect\"], 100); // Pequeno delay para garantir a ordem correta de execução\n            } else if (selectedDate) {\n                var _selectedDate_start, _selectedDate_end;\n                // Novo agendamento\n                // Usar os objetos Date diretamente, mantendo o horário local que o usuário clicou\n                console.log(\"[MODAL-DEBUG] selectedDate recebido:\", {\n                    start: (_selectedDate_start = selectedDate.start) === null || _selectedDate_start === void 0 ? void 0 : _selectedDate_start.toLocaleString(),\n                    end: (_selectedDate_end = selectedDate.end) === null || _selectedDate_end === void 0 ? void 0 : _selectedDate_end.toLocaleString(),\n                    temEnd: !!selectedDate.end,\n                    tipo: typeof selectedDate\n                });\n                // Para a data de início, usar o objeto Date do calendário diretamente\n                const startDate = selectedDate.start;\n                // Para a data de término, SEMPRE usar duração de 1 hora, ignorando o valor de end do calendário\n                console.log(\"[MODAL-DEBUG] FOR\\xc7ANDO endDate com dura\\xe7\\xe3o de 60 minutos, ignorando o valor de end\");\n                const endDate = new Date(selectedDate.start.getTime() + 60 * 60 * 1000);\n                console.log(\"[MODAL-DEBUG] Dura\\xe7\\xe3o for\\xe7ada (minutos): \".concat((endDate - startDate) / (60 * 1000)));\n                console.log(\"[MODAL-INIT] Datas do calend\\xe1rio (hor\\xe1rio local) - Start: \".concat(startDate.toLocaleString(), \", End: \").concat(endDate.toLocaleString()));\n                // Converter para strings ISO apenas para armazenamento no formData\n                // Importante: toISOString() converte para UTC, mas vamos usar isso apenas para armazenamento interno\n                const startISOString = startDate.toISOString();\n                const endISOString = endDate.toISOString();\n                setFormData({\n                    title: \"\",\n                    description: \"\",\n                    specialty: \"\",\n                    providerId: \"\",\n                    personId: \"\",\n                    locationId: \"\",\n                    serviceTypeId: \"\",\n                    insuranceId: \"\",\n                    startDate: startISOString,\n                    endDate: endISOString,\n                    sequentialAppointments: 1,\n                    status: \"PENDING\",\n                    recurrence: {\n                        enabled: false,\n                        type: \"OCCURRENCES\",\n                        numberOfOccurrences: 1,\n                        endDate: null,\n                        patterns: []\n                    }\n                });\n            }\n        }\n    }[\"AppointmentModal.useEffect\"], [\n        selectedAppointment,\n        selectedDate,\n        providers\n    ]);\n    // Carregar tipos de serviço com base no convênio selecionado ou todos os tipos se não houver convênio\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AppointmentModal.useEffect\": ()=>{\n            const loadServiceTypes = {\n                \"AppointmentModal.useEffect.loadServiceTypes\": async ()=>{\n                    // Se não tiver paciente, limpar tipos de serviço e retornar\n                    if (!formData.personId) {\n                        setServiceTypes([]);\n                        // Se não estamos editando um agendamento existente, resetar apenas o tipo de serviço\n                        if (!formData.id && formData.serviceTypeId) {\n                            setFormData({\n                                \"AppointmentModal.useEffect.loadServiceTypes\": (prev)=>({\n                                        ...prev,\n                                        serviceTypeId: \"\"\n                                    })\n                            }[\"AppointmentModal.useEffect.loadServiceTypes\"]);\n                        }\n                        return;\n                    }\n                    setIsLoading(true);\n                    try {\n                        console.log(\"Carregando tipos de servi\\xe7o para o paciente ID: \".concat(formData.personId, \" e conv\\xeanio ID: \").concat(formData.insuranceId));\n                        // Buscar apenas os tipos de serviço que têm limites configurados para esta pessoa e convênio\n                        const serviceTypesFromLimits = await _app_modules_scheduler_services_insuranceServiceLimitService__WEBPACK_IMPORTED_MODULE_7__[\"default\"].getServiceTypesByInsurance(formData.insuranceId, formData.personId);\n                        if (serviceTypesFromLimits && serviceTypesFromLimits.length > 0) {\n                            console.log(\"Encontrados \".concat(serviceTypesFromLimits.length, \" tipos de servi\\xe7o com limites configurados\"));\n                            setServiceTypes(serviceTypesFromLimits);\n                            // Se estamos editando um agendamento existente e o tipo de serviço não está na lista,\n                            // não limpar o tipo de serviço selecionado\n                            if (formData.id && formData.serviceTypeId) {\n                                const serviceTypeExists = serviceTypesFromLimits.some({\n                                    \"AppointmentModal.useEffect.loadServiceTypes.serviceTypeExists\": (type)=>type.id === formData.serviceTypeId\n                                }[\"AppointmentModal.useEffect.loadServiceTypes.serviceTypeExists\"]);\n                                if (!serviceTypeExists) {\n                                    console.log(\"Tipo de servi\\xe7o \".concat(formData.serviceTypeId, \" n\\xe3o encontrado na lista, mas mantendo-o selecionado para edi\\xe7\\xe3o\"));\n                                }\n                            }\n                        // Removido o reset do tipo de serviço para novos agendamentos\n                        // Removido o código que limpava mensagens de erro\n                        } else {\n                            console.warn(\"Nenhum tipo de serviço configurado para este paciente e convênio\");\n                            setServiceTypes([]);\n                            // Se não estamos editando e temos um tipo de serviço selecionado, limpar apenas o tipo de serviço\n                            if (!formData.id && formData.serviceTypeId) {\n                                setFormData({\n                                    \"AppointmentModal.useEffect.loadServiceTypes\": (prev)=>({\n                                            ...prev,\n                                            serviceTypeId: \"\"\n                                        })\n                                }[\"AppointmentModal.useEffect.loadServiceTypes\"]);\n                            }\n                            // Exibir mensagem para o usuário\n                            toast_error(\"Não há tipos de serviço configurados para este paciente e convênio. \" + \"Por favor, configure os limites de serviço para esta combinação de paciente e convênio.\");\n                        }\n                    } catch (error) {\n                        console.error(\"Erro ao carregar tipos de serviço:\", error);\n                        setServiceTypes([]);\n                        toast_error(\"Erro ao carregar tipos de serviço. Por favor, tente novamente.\");\n                    } finally{\n                        setIsLoading(false);\n                    }\n                }\n            }[\"AppointmentModal.useEffect.loadServiceTypes\"];\n            loadServiceTypes();\n        }\n    }[\"AppointmentModal.useEffect\"], [\n        formData.insuranceId,\n        formData.personId,\n        formData.id,\n        formData.serviceTypeId,\n        toast_error\n    ]);\n    // Verificar limites de convênio\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AppointmentModal.useEffect\": ()=>{\n            const checkInsuranceLimits = {\n                \"AppointmentModal.useEffect.checkInsuranceLimits\": async ()=>{\n                    // Só verifica se tiver pessoa, convênio e tipo de serviço selecionados\n                    if (formData.personId && formData.insuranceId && formData.serviceTypeId && formData.startDate) {\n                        setIsCheckingLimits(true);\n                        try {\n                            console.log(\"Verificando limites para data do agendamento: \".concat(formData.startDate));\n                            const result = await _app_modules_scheduler_services_insuranceLimitService__WEBPACK_IMPORTED_MODULE_6__[\"default\"].checkLimitAvailability({\n                                personId: formData.personId,\n                                insuranceId: formData.insuranceId,\n                                serviceTypeId: formData.serviceTypeId,\n                                appointmentDate: new Date(formData.startDate) // Passar a data do agendamento\n                            });\n                            console.log(\"Resultado da verifica\\xe7\\xe3o de limites:\", result);\n                            setLimitInfo(result.usage);\n                            // Se limite foi atingido, mostrar erro apenas para novos agendamentos\n                            // Para edição, apenas mostrar um aviso informativo\n                            if (!result.available) {\n                                if (!isEditMode) {\n                                    toast_error(result.message);\n                                } else {\n                                    // Para edição, mostrar um aviso mais suave\n                                    console.log(\"Limite atingido, mas permitindo edição do agendamento existente\");\n                                }\n                            }\n                        } catch (error) {\n                            console.error(\"Erro ao verificar limites:\", error);\n                        } finally{\n                            setIsCheckingLimits(false);\n                        }\n                    } else {\n                        // Limpar dados de limite se algum campo necessário não estiver preenchido\n                        setLimitInfo(null);\n                    }\n                }\n            }[\"AppointmentModal.useEffect.checkInsuranceLimits\"];\n            checkInsuranceLimits();\n        }\n    }[\"AppointmentModal.useEffect\"], [\n        formData.personId,\n        formData.insuranceId,\n        formData.serviceTypeId,\n        formData.startDate,\n        toast_error,\n        isEditMode\n    ]);\n    // Validar agendamento antes de enviar\n    const validateAppointment = async function() {\n        let data = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : formData;\n        // Validação básica\n        if (!data.title) {\n            toast_error(\"Por favor, informe um título para o agendamento.\");\n            return false;\n        }\n        if (!data.providerId) {\n            toast_error(\"Por favor, selecione um profissional.\");\n            return false;\n        }\n        if (!data.personId) {\n            toast_error(\"Por favor, selecione um paciente.\");\n            return false;\n        }\n        if (!data.locationId) {\n            toast_error(\"Por favor, selecione um local.\");\n            return false;\n        }\n        if (!data.serviceTypeId) {\n            toast_error(\"Por favor, selecione um tipo de serviço.\");\n            return false;\n        }\n        // Verificar limites de convênio apenas para novos agendamentos\n        // Para edição, permitir mesmo que o limite tenha sido atingido\n        if (!isEditMode && limitInfo && limitInfo.monthly && !limitInfo.monthly.unlimited) {\n            const isLimitReached = limitInfo.monthly.used >= limitInfo.monthly.limit;\n            if (isLimitReached) {\n                console.log(\"Limite de convênio atingido, bloqueando criação de novo agendamento\");\n                toast_error(\"Limite mensal de \".concat(limitInfo.monthly.limit, \" agendamentos atingido. Voc\\xea j\\xe1 utilizou \").concat(limitInfo.monthly.used, \" agendamentos.\"));\n                return false;\n            }\n        }\n        // Verificar se há conflitos com agendamentos existentes\n        try {\n            // Verificar disponibilidade tanto para novos agendamentos quanto para edição\n            // Verificar se o horário está disponível para o profissional no servidor\n            const providerCheckResponse = await _app_modules_scheduler_services_appointmentService__WEBPACK_IMPORTED_MODULE_5__.appointmentService.checkAvailability({\n                providerId: data.providerId,\n                startDate: data.startDate,\n                endDate: data.endDate,\n                excludeId: data.id // Para edição, excluir o próprio agendamento\n            });\n            if (!providerCheckResponse.available) {\n                if (providerCheckResponse.conflict) {\n                    toast_error(\"Horário indisponível para o profissional selecionado.\");\n                } else {\n                    toast_error(\"Horário indisponível para o profissional.\");\n                }\n                return false;\n            }\n            // Verificar se o paciente já tem um agendamento no mesmo horário\n            const patientCheckResponse = await _app_modules_scheduler_services_appointmentService__WEBPACK_IMPORTED_MODULE_5__.appointmentService.checkPatientAvailability({\n                personId: data.personId,\n                startDate: data.startDate,\n                endDate: data.endDate,\n                excludeId: data.id // Para edição, excluir o próprio agendamento\n            });\n            if (!patientCheckResponse.available) {\n                if (patientCheckResponse.conflict) {\n                    toast_error(\"Paciente já possui um agendamento neste horário.\");\n                } else {\n                    toast_error(\"Horário indisponível para o paciente.\");\n                }\n                return false;\n            }\n        } catch (error) {\n            console.error(\"Erro ao verificar disponibilidade no servidor:\", error);\n        // Não bloquear o fluxo em caso de erro na verificação\n        }\n        // Validar recorrência, se estiver habilitada (apenas para novos agendamentos)\n        if (!isEditMode && data.recurrence.enabled) {\n            // Validar padrões de recorrência\n            if (!data.recurrence.patterns || data.recurrence.patterns.length === 0) {\n                toast_error(\"Por favor, selecione pelo menos um dia da semana para a recorrência.\");\n                return false;\n            }\n            // Validar data final ou número de ocorrências\n            if (data.recurrence.type === 'END_DATE') {\n                if (!data.recurrence.endDate) {\n                    toast_error(\"Por favor, informe uma data final para a recorrência.\");\n                    return false;\n                }\n                const endDate = new Date(data.recurrence.endDate);\n                const tomorrow = new Date();\n                tomorrow.setDate(tomorrow.getDate() + 1);\n                if (endDate <= tomorrow) {\n                    toast_error(\"A data final da recorrência deve ser pelo menos 1 dia após a data atual.\");\n                    return false;\n                }\n            } else if (data.recurrence.type === 'OCCURRENCES') {\n                if (!data.recurrence.numberOfOccurrences || data.recurrence.numberOfOccurrences < 1) {\n                    toast_error(\"Por favor, informe um número válido de ocorrências para a recorrência.\");\n                    return false;\n                }\n            }\n        }\n        // Verificar disponibilidade do profissional, se implementado\n        if (checkAvailability) {\n            const isAvailable = await checkAvailability(data.providerId, {\n                start: data.startDate,\n                end: data.endDate\n            });\n            if (!isAvailable) {\n                toast_error(\"O profissional selecionado não está disponível neste horário.\");\n                return false;\n            }\n            // Se estiver criando agendamentos sequenciais (apenas para novos agendamentos), verificar cada um deles\n            if (!isEditMode && data.sequentialAppointments > 1) {\n                // Converter strings ISO para objetos Date para cálculos\n                const startDate = new Date(data.startDate);\n                const endDate = new Date(data.endDate);\n                let currentEndDate = endDate;\n                // Duração fixa de 1 hora (3600000 ms) para agendamentos sequenciais\n                const sequentialDuration = 3600000; // 1 hora em milissegundos\n                console.log(\"[SEQUENTIAL] Verificando \".concat(data.sequentialAppointments, \" agendamentos sequenciais\"));\n                console.log(\"[SEQUENTIAL] Agendamento inicial: \".concat(startDate.toLocaleString(), \" - \").concat(endDate.toLocaleString()));\n                for(let i = 1; i < data.sequentialAppointments; i++){\n                    // Criar o próximo agendamento sequencial\n                    // Começa exatamente quando o anterior termina\n                    const nextStartDate = new Date(currentEndDate.getTime());\n                    // Duração fixa de 1 hora\n                    const nextEndDate = new Date(nextStartDate.getTime() + sequentialDuration);\n                    console.log(\"[SEQUENTIAL] Verificando agendamento #\".concat(i + 1, \": \").concat(nextStartDate.toLocaleString(), \" - \").concat(nextEndDate.toLocaleString()));\n                    // Converter para strings ISO para verificar disponibilidade\n                    const nextStartISO = nextStartDate.toISOString();\n                    const nextEndISO = nextEndDate.toISOString();\n                    // Verificar disponibilidade do profissional no servidor para este horário sequencial\n                    try {\n                        const providerServerCheckResponse = await _app_modules_scheduler_services_appointmentService__WEBPACK_IMPORTED_MODULE_5__.appointmentService.checkAvailability({\n                            providerId: data.providerId,\n                            startDate: nextStartISO,\n                            endDate: nextEndISO,\n                            excludeId: data.id // Para edição, excluir o próprio agendamento\n                        });\n                        // Se não estiver disponível no servidor, mostrar mensagem de erro\n                        if (!providerServerCheckResponse.available) {\n                            // Formatar as datas para exibição\n                            const formatTime = (date)=>(0,_barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_19__.format)(date, 'HH:mm');\n                            if (providerServerCheckResponse.conflict) {\n                                const conflict = providerServerCheckResponse.conflict;\n                                const conflictStart = new Date(conflict.startDate);\n                                const conflictEnd = new Date(conflict.endDate);\n                                const appointmentTitle = conflict.title || \"Sem título\";\n                                toast_error(\"O \".concat(i + 1, \"\\xba agendamento sequencial (\").concat(formatTime(nextStartDate), \" - \").concat(formatTime(nextEndDate), ') conflita com o agendamento \"').concat(appointmentTitle, '\" (').concat((0,_barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_19__.format)(conflictStart, 'HH:mm'), \" - \").concat((0,_barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_19__.format)(conflictEnd, 'HH:mm'), \").\"));\n                            } else {\n                                toast_error(\"O profissional n\\xe3o est\\xe1 dispon\\xedvel para o \".concat(i + 1, \"\\xba agendamento sequencial (\").concat(formatTime(nextStartDate), \" - \").concat(formatTime(nextEndDate), \").\"));\n                            }\n                            return false;\n                        }\n                        // Verificar disponibilidade do paciente no servidor para este horário sequencial\n                        const patientServerCheckResponse = await _app_modules_scheduler_services_appointmentService__WEBPACK_IMPORTED_MODULE_5__.appointmentService.checkPatientAvailability({\n                            personId: data.personId,\n                            startDate: nextStartISO,\n                            endDate: nextEndISO,\n                            excludeId: data.id // Para edição, excluir o próprio agendamento\n                        });\n                        // Se não estiver disponível no servidor, mostrar mensagem de erro\n                        if (!patientServerCheckResponse.available) {\n                            // Formatar as datas para exibição\n                            const formatTime = (date)=>(0,_barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_19__.format)(date, 'HH:mm');\n                            if (patientServerCheckResponse.conflict) {\n                                const conflict = patientServerCheckResponse.conflict;\n                                const conflictStart = new Date(conflict.startDate);\n                                const conflictEnd = new Date(conflict.endDate);\n                                const appointmentTitle = conflict.title || \"Sem título\";\n                                toast_error('O paciente j\\xe1 possui um agendamento \"'.concat(appointmentTitle, '\" (').concat((0,_barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_19__.format)(conflictStart, 'HH:mm'), \" - \").concat((0,_barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_19__.format)(conflictEnd, 'HH:mm'), \") que conflita com o \").concat(i + 1, \"\\xba agendamento sequencial (\").concat(formatTime(nextStartDate), \" - \").concat(formatTime(nextEndDate), \").\"));\n                            } else {\n                                toast_error(\"O paciente j\\xe1 possui um agendamento no hor\\xe1rio do \".concat(i + 1, \"\\xba agendamento sequencial (\").concat(formatTime(nextStartDate), \" - \").concat(formatTime(nextEndDate), \").\"));\n                            }\n                            return false;\n                        }\n                    } catch (error) {\n                        console.error(\"Erro ao verificar disponibilidade do \".concat(i + 1, \"\\xba agendamento sequencial:\"), error);\n                    // Continuar com a verificação local em caso de erro na verificação do servidor\n                    }\n                    // Verificar disponibilidade local (horários de trabalho)\n                    const isNextSlotAvailable = await checkAvailability(data.providerId, {\n                        start: nextStartISO,\n                        end: nextEndISO\n                    });\n                    if (!isNextSlotAvailable) {\n                        toast_error(\"O profissional n\\xe3o est\\xe1 dispon\\xedvel para o \".concat(i + 1, \"\\xba agendamento sequencial (\").concat((0,_barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_19__.format)(nextStartDate, 'HH:mm'), \" - \").concat((0,_barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_19__.format)(nextEndDate, 'HH:mm'), \").\"));\n                        return false;\n                    }\n                    currentEndDate = nextEndDate;\n                }\n            }\n        }\n        return true;\n    };\n    // Função para confirmar a ação antes de executar\n    const confirmAction = async ()=>{\n        if (!actionToConfirm) {\n            return;\n        }\n        console.log('[CONFIRM-ACTION] Confirmando ação:', actionToConfirm.type);\n        try {\n            // Fechar o diálogo de confirmação imediatamente\n            setConfirmationDialogOpen(false);\n            // Garantir que o modal principal possa ser fechado\n            if (actionToConfirm.type === \"save\") {\n                await saveAppointment();\n            } else if (actionToConfirm.type === \"delete\") {\n                await deleteAppointment();\n            }\n        } catch (error) {\n            console.error('[CONFIRM-ACTION] Erro ao executar ação:', error);\n        // Garantir que o modal possa ser fechado mesmo em caso de erro\n        } finally{\n            // Garantir que o modal possa ser fechado após a ação, independente do resultado\n            setTimeout(()=>{\n                console.log('[CONFIRM-ACTION] Garantindo que o modal possa ser fechado após a ação');\n            }, 500);\n        }\n    };\n    // Função para salvar o agendamento após confirmação\n    const saveAppointment = async ()=>{\n        setIsLoading(true);\n        try {\n            // As datas no formData estão em formato ISO string (UTC)\n            // Isso é exatamente o que o backend espera, então não precisamos converter\n            console.log(\"[SAVE] Datas originais (UTC) - Start: \".concat(formData.startDate, \", End: \").concat(formData.endDate));\n            // Converter para objetos Date para exibir no log o horário local\n            const startLocalDate = new Date(formData.startDate);\n            const endLocalDate = new Date(formData.endDate);\n            console.log(\"[SAVE] Datas locais - Start: \".concat(startLocalDate.toLocaleString(), \", End: \").concat(endLocalDate.toLocaleString()));\n            // Criar objeto base com dados comuns\n            const baseAppointmentData = {\n                title: formData.title,\n                description: formData.description,\n                userId: formData.providerId,\n                providerId: formData.providerId,\n                personId: formData.personId,\n                locationId: formData.locationId,\n                serviceTypeId: formData.serviceTypeId,\n                insuranceId: formData.insuranceId || null,\n                // Enviar as datas em formato ISO (UTC) para o backend\n                startDate: formData.startDate,\n                endDate: formData.endDate,\n                creatorId: formData.providerId,\n                status: formData.status\n            };\n            // Adicionar sequentialAppointments apenas para novos agendamentos\n            const appointmentData = isEditMode ? baseAppointmentData : {\n                ...baseAppointmentData,\n                sequentialAppointments: formData.sequentialAppointments\n            };\n            console.log(\"[SAVE] Enviando datas (UTC) - Start: \".concat(appointmentData.startDate, \", End: \").concat(appointmentData.endDate));\n            if (!isEditMode && formData.recurrence.enabled) {\n                // Preparar dados de recorrência\n                const recurrenceData = {\n                    ...appointmentData,\n                    recurrenceType: formData.recurrence.type,\n                    // Para o tipo OCCURRENCES, usar o número de ocorrências\n                    // Para o tipo END_DATE, usar a data final (já em formato ISO)\n                    recurrenceValue: formData.recurrence.type === \"OCCURRENCES\" ? formData.recurrence.numberOfOccurrences : formData.recurrence.endDate,\n                    // Mapear os padrões de recorrência\n                    patterns: formData.recurrence.patterns.map((pattern)=>{\n                        console.log(\"[RECURRENCE] Padr\\xe3o: dia \".concat(pattern.dayOfWeek, \", \").concat(pattern.startTime, \" - \").concat(pattern.endTime));\n                        return {\n                            dayOfWeek: pattern.dayOfWeek,\n                            startTime: pattern.startTime,\n                            endTime: pattern.endTime\n                        };\n                    })\n                };\n                console.log(\"[RECURRENCE] Criando recorr\\xeancia do tipo \".concat(recurrenceData.recurrenceType));\n                console.log(\"[RECURRENCE] Valor: \".concat(recurrenceData.recurrenceValue));\n                console.log(\"[RECURRENCE] Padr\\xf5es: \".concat(recurrenceData.patterns.length));\n                await _app_modules_scheduler_services_appointmentService__WEBPACK_IMPORTED_MODULE_5__.appointmentService.createRecurrence(recurrenceData);\n            } else {\n                if (formData.id) {\n                    // Verificar se o ID é válido antes de atualizar\n                    if (!formData.id || formData.id === \"undefined\") {\n                        console.error(\"[SAVE] ID do agendamento inválido para atualização:\", formData.id);\n                        throw new Error(\"ID do agendamento inválido. Não é possível atualizar.\");\n                    }\n                    console.log(\"[SAVE] Atualizando agendamento com ID:\", formData.id);\n                    try {\n                        // Update existente\n                        await _app_modules_scheduler_services_appointmentService__WEBPACK_IMPORTED_MODULE_5__.appointmentService.updateAppointment(formData.id, {\n                            ...appointmentData,\n                            updatedBy: formData.providerId\n                        });\n                    } catch (updateError) {\n                        console.error(\"[SAVE] Erro ao atualizar agendamento:\", updateError);\n                        // Verificar se o erro é 404 (agendamento não encontrado)\n                        if (updateError.response && updateError.response.status === 404) {\n                            console.log(\"[SAVE] Agendamento não encontrado, tentando criar um novo\");\n                            // Tentar criar um novo agendamento com os mesmos dados\n                            await _app_modules_scheduler_services_appointmentService__WEBPACK_IMPORTED_MODULE_5__.appointmentService.createAppointment(appointmentData);\n                        } else {\n                            // Se não for 404, propagar o erro\n                            throw updateError;\n                        }\n                    }\n                } else {\n                    // Novo agendamento\n                    await _app_modules_scheduler_services_appointmentService__WEBPACK_IMPORTED_MODULE_5__.appointmentService.createAppointment(appointmentData);\n                }\n            }\n            // Exibir mensagem de sucesso\n            let successMessage = \"\";\n            if (isEditMode) {\n                successMessage = \"Agendamento atualizado com sucesso!\";\n            } else if (formData.recurrence.enabled) {\n                successMessage = \"Recorrência criada com sucesso!\";\n            } else {\n                successMessage = \"Agendamento criado com sucesso!\";\n            }\n            toast_success({\n                title: \"Sucesso\",\n                message: successMessage\n            });\n            onAppointmentChange();\n            onClose(); // Fechar o modal de agendamento apenas após sucesso\n        } catch (error) {\n            console.error(\"Erro ao salvar agendamento:\", error);\n            // Extrair mensagem de erro da resposta da API, se disponível\n            let errorMsg = \"Erro ao salvar o agendamento. Por favor, tente novamente.\";\n            if (error.response && error.response.data) {\n                if (error.response.data.message) {\n                    errorMsg = error.response.data.message;\n                    // Adicionar informações mais detalhadas para conflitos de horário\n                    if (error.response.data.reason === \"CONFLICT\" && error.response.data.conflictData) {\n                        const conflict = error.response.data.conflictData;\n                        const conflictStart = new Date(conflict.startDate);\n                        const conflictEnd = new Date(conflict.endDate);\n                        // Formatar as datas para exibição\n                        const formatDate = (date)=>{\n                            try {\n                                if (!date) return \"horário não especificado\";\n                                // Verificar se a data é válida\n                                if (isNaN(date.getTime())) return \"horário não disponível\";\n                                return date.toLocaleTimeString([], {\n                                    hour: '2-digit',\n                                    minute: '2-digit'\n                                });\n                            } catch (error) {\n                                console.error(\"Erro ao formatar data:\", error, date);\n                                return \"horário não disponível\";\n                            }\n                        };\n                        const appointmentTitle = conflict.title || \"Sem título\";\n                        errorMsg += ' Existe um agendamento conflitante \"'.concat(appointmentTitle, '\" no per\\xedodo ').concat(formatDate(conflictStart), \" - \").concat(formatDate(conflictEnd), \".\");\n                    }\n                } else if (error.response.data.error) {\n                    errorMsg = error.response.data.error;\n                }\n            } else if (error.message) {\n                errorMsg = error.message;\n            }\n            // Exibir toast de erro\n            toast_error({\n                title: \"Erro ao salvar agendamento\",\n                message: errorMsg\n            });\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    // Função para excluir agendamento após confirmação\n    const deleteAppointment = async ()=>{\n        // Verificar se o ID é válido antes de excluir\n        if (!formData.id || formData.id === \"undefined\") {\n            console.error(\"[DELETE] ID do agendamento inválido para exclusão:\", formData.id);\n            toast_error(\"ID do agendamento inválido. Não é possível excluir.\");\n            return;\n        }\n        setIsLoading(true);\n        console.log(\"[DELETE] Excluindo agendamento com ID:\", formData.id);\n        try {\n            try {\n                await _app_modules_scheduler_services_appointmentService__WEBPACK_IMPORTED_MODULE_5__.appointmentService.deleteAppointment(formData.id);\n            } catch (deleteError) {\n                console.error(\"[DELETE] Erro ao excluir agendamento:\", deleteError);\n                // Verificar se o erro é 404 (agendamento não encontrado)\n                if (deleteError.response && deleteError.response.status === 404) {\n                    // Se o agendamento não existe, considerar como excluído com sucesso\n                    console.log(\"[DELETE] Agendamento não encontrado, considerando como excluído\");\n                // Não propagar o erro, continuar como se tivesse excluído com sucesso\n                } else {\n                    // Se não for 404, propagar o erro\n                    throw deleteError;\n                }\n            }\n            // Exibir mensagem de sucesso\n            toast_success({\n                title: \"Sucesso\",\n                message: \"Agendamento excluído com sucesso!\"\n            });\n            onAppointmentChange();\n            onClose(); // Fechar o modal de agendamento apenas após sucesso\n        } catch (error) {\n            console.error(\"Erro ao excluir agendamento:\", error);\n            // Extrair mensagem de erro da resposta da API, se disponível\n            let errorMsg = \"Erro ao excluir o agendamento. Por favor, tente novamente.\";\n            if (error.response && error.response.data) {\n                if (error.response.data.message) {\n                    errorMsg = error.response.data.message;\n                } else if (error.response.data.error) {\n                    errorMsg = error.response.data.error;\n                }\n            } else if (error.message) {\n                errorMsg = error.message;\n            }\n            // Exibir toast de erro\n            toast_error({\n                title: \"Erro ao excluir agendamento\",\n                message: errorMsg\n            });\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        // Verificar novamente a permissão antes de enviar\n        if (!hasPermission || isReadOnlyMode) {\n            toast_error(isEditMode ? \"Você não tem permissão para editar agendamentos.\" : \"Você não tem permissão para criar agendamentos.\");\n            return;\n        }\n        // Validar dados do agendamento\n        const isValid = await validateAppointment();\n        if (!isValid) {\n            return;\n        }\n        // Preparar mensagem de confirmação\n        let actionMessage = \"\";\n        let actionTitle = \"\";\n        if (isEditMode) {\n            // Mensagem para edição de agendamento existente\n            actionTitle = \"Atualizar agendamento\";\n            actionMessage = 'Deseja atualizar o agendamento \"'.concat(formData.title, '\"?');\n        } else if (formData.recurrence.enabled) {\n            // Mensagem para criação de agendamento recorrente\n            actionTitle = \"Criar agendamentos recorrentes\";\n            actionMessage = \"Deseja criar uma s\\xe9rie de agendamentos recorrentes para \".concat(formData.title, \"?\");\n        } else {\n            // Mensagem para criação de agendamento normal ou sequencial\n            actionTitle = formData.sequentialAppointments > 1 ? \"Criar agendamentos sequenciais\" : \"Criar agendamento\";\n            actionMessage = formData.sequentialAppointments > 1 ? \"Deseja criar \".concat(formData.sequentialAppointments, ' agendamentos sequenciais para \"').concat(formData.title, '\"?') : 'Deseja criar o agendamento \"'.concat(formData.title, '\"?');\n        }\n        // Configurar ação para confirmação\n        setActionToConfirm({\n            type: \"save\",\n            message: actionMessage,\n            title: actionTitle,\n            variant: \"info\"\n        });\n        // Abrir diálogo de confirmação\n        setConfirmationDialogOpen(true);\n    };\n    // Função para solicitar confirmação de exclusão\n    const handleDelete = ()=>{\n        // Verificar permissão de exclusão\n        if (!canDelete) {\n            toast_error(\"Você não tem permissão para excluir agendamentos.\");\n            return;\n        }\n        if (!formData.id) return;\n        // Configurar ação para confirmação\n        setActionToConfirm({\n            type: \"delete\",\n            message: 'Tem certeza que deseja excluir o agendamento \"'.concat(formData.title, '\"?'),\n            title: \"Excluir agendamento\",\n            variant: \"danger\"\n        });\n        // Abrir diálogo de confirmação\n        setConfirmationDialogOpen(true);\n    };\n    // Função para calcular o horário do último agendamento sequencial\n    const calculateLastAppointmentTime = ()=>{\n        if (formData.sequentialAppointments <= 1 || !formData.startDate || !formData.endDate) {\n            return null;\n        }\n        // Converter strings ISO para objetos Date para cálculos\n        const endDate = new Date(formData.endDate);\n        // Duração fixa de 1 hora (3600000 ms) para agendamentos sequenciais\n        const sequentialDuration = 3600000; // 1 hora em milissegundos\n        // Começar com o horário de término do primeiro agendamento\n        let currentEndTime = endDate;\n        // Calcular o horário do último agendamento\n        for(let i = 1; i < formData.sequentialAppointments; i++){\n            // O próximo agendamento começa quando o anterior termina\n            const nextStartTime = new Date(currentEndTime.getTime());\n            // Duração fixa de 1 hora\n            const nextEndTime = new Date(nextStartTime.getTime() + sequentialDuration);\n            // Atualizar para o próximo agendamento\n            currentEndTime = nextEndTime;\n        }\n        // O último agendamento começa 1 hora antes do horário final calculado\n        const lastStartTime = new Date(currentEndTime.getTime() - sequentialDuration);\n        const lastEndTime = currentEndTime;\n        console.log(\"[SEQUENTIAL-CALC] \\xdaltimo agendamento: \".concat(lastStartTime.toLocaleString(), \" - \").concat(lastEndTime.toLocaleString()));\n        // Retornar os objetos Date diretamente\n        return {\n            start: lastStartTime,\n            end: lastEndTime\n        };\n    };\n    // Calcular o último horário para os agendamentos sequenciais\n    // Usar useEffect para recalcular sempre que formData mudar\n    const [lastAppointmentTime, setLastAppointmentTime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AppointmentModal.useEffect\": ()=>{\n            setLastAppointmentTime(calculateLastAppointmentTime());\n        }\n    }[\"AppointmentModal.useEffect\"], [\n        formData.sequentialAppointments,\n        formData.startDate,\n        formData.endDate\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ConfirmationDialog__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                isOpen: confirmationDialogOpen,\n                onClose: ()=>{\n                    console.log('[CONFIRMATION-DIALOG] Fechando diálogo de confirmação');\n                    // Fechar o diálogo de confirmação\n                    setConfirmationDialogOpen(false);\n                },\n                onConfirm: confirmAction,\n                title: (actionToConfirm === null || actionToConfirm === void 0 ? void 0 : actionToConfirm.title) || \"Confirmar ação\",\n                message: (actionToConfirm === null || actionToConfirm === void 0 ? void 0 : actionToConfirm.message) || \"\",\n                variant: (actionToConfirm === null || actionToConfirm === void 0 ? void 0 : actionToConfirm.variant) || \"info\",\n                confirmText: (actionToConfirm === null || actionToConfirm === void 0 ? void 0 : actionToConfirm.type) === \"delete\" ? \"Excluir\" : \"Confirmar\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\calendar\\\\AppointmentModal.js\",\n                lineNumber: 1259,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ModuleModal__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                isOpen: isOpen,\n                onClose: ()=>{\n                    // Sempre usar forceCloseModal para garantir o fechamento\n                    forceCloseModal();\n                },\n                animateExit: false,\n                title: selectedAppointment ? \"Editar Agendamento\" : \"Novo Agendamento\",\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                    size: 22\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\calendar\\\\AppointmentModal.js\",\n                    lineNumber: 1281,\n                    columnNumber: 15\n                }, void 0),\n                moduleColor: \"scheduler\",\n                size: \"xl\",\n                preventClose: false,\n                onInteractOutside: ()=>{\n                    console.log('[OUTSIDE-CLICK] Clique fora do modal');\n                    // Sempre permitir o fechamento do modal\n                    forceCloseModal();\n                },\n                footer: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_appointmentModal_ModuleModalFooter__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                    isEditMode: isEditMode,\n                    canDelete: canDelete && !isReadOnlyMode,\n                    hasPermission: hasPermission && !isReadOnlyMode,\n                    formData: formData,\n                    isLoading: isLoading,\n                    limitInfo: limitInfo,\n                    handleDelete: handleDelete,\n                    onClose: ()=>{\n                        console.log('[FOOTER-CLOSE] Tentativa de fechar o modal via footer');\n                        forceCloseModal(); // Forçar fechamento independente do estado\n                    }\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\calendar\\\\AppointmentModal.js\",\n                    lineNumber: 1291,\n                    columnNumber: 11\n                }, void 0),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                    id: \"appointment-form\",\n                    onSubmit: handleSubmit,\n                    className: \"overflow-y-auto dark:bg-gray-800 flex flex-col justify-between p-5\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-5\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_appointmentModal_BasicInfoForm__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                            formData: formData,\n                                            setFormData: setFormData,\n                                            readOnly: isReadOnlyMode\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\calendar\\\\AppointmentModal.js\",\n                                            lineNumber: 1314,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_appointmentModal_SpecialtySelector__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                            formData: formData,\n                                            setFormData: setFormData,\n                                            specialties: specialties,\n                                            readOnly: isReadOnlyMode\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\calendar\\\\AppointmentModal.js\",\n                                            lineNumber: 1321,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_appointmentModal_AppointmentSelectors__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            formData: formData,\n                                            setFormData: setFormData,\n                                            filteredProviders: filteredProviders,\n                                            persons: persons,\n                                            locations: locations,\n                                            serviceTypes: serviceTypes,\n                                            personInsurances: personInsurances,\n                                            isLoadingInsurances: isLoadingInsurances,\n                                            isLoading: isLoading,\n                                            readOnly: isReadOnlyMode\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\calendar\\\\AppointmentModal.js\",\n                                            lineNumber: 1329,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\calendar\\\\AppointmentModal.js\",\n                                    lineNumber: 1312,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-5\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_appointmentModal_AppointmentDates__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                            formData: formData,\n                                            setFormData: setFormData,\n                                            readOnly: isReadOnlyMode\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\calendar\\\\AppointmentModal.js\",\n                                            lineNumber: 1345,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        isEditMode && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_appointmentModal_AppointmentStatus__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                            formData: formData,\n                                            setFormData: setFormData,\n                                            readOnly: isReadOnlyMode\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\calendar\\\\AppointmentModal.js\",\n                                            lineNumber: 1353,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        !isEditMode && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mb-8\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_appointmentModal_SequentialAppointments__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                formData: formData,\n                                                setFormData: setFormData,\n                                                lastAppointmentTime: lastAppointmentTime\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\calendar\\\\AppointmentModal.js\",\n                                                lineNumber: 1363,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\calendar\\\\AppointmentModal.js\",\n                                            lineNumber: 1362,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        !isEditMode && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_appointmentModal_RecurrenceSettings__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                            formData: formData,\n                                            setFormData: setFormData\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\calendar\\\\AppointmentModal.js\",\n                                            lineNumber: 1373,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        formData.personId && formData.insuranceId && formData.serviceTypeId && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_InsuranceLimitsDisplay__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                            limitInfo: limitInfo,\n                                            isLoading: isCheckingLimits,\n                                            isEditMode: isEditMode\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\calendar\\\\AppointmentModal.js\",\n                                            lineNumber: 1381,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\calendar\\\\AppointmentModal.js\",\n                                    lineNumber: 1343,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\calendar\\\\AppointmentModal.js\",\n                            lineNumber: 1311,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\calendar\\\\AppointmentModal.js\",\n                        lineNumber: 1310,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\calendar\\\\AppointmentModal.js\",\n                    lineNumber: 1307,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\calendar\\\\AppointmentModal.js\",\n                lineNumber: 1273,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true);\n};\n_s(AppointmentModal, \"ZoEZAigQHwIJpzhwwu4gfJhoH9Y=\", false, function() {\n    return [\n        _contexts_ToastContext__WEBPACK_IMPORTED_MODULE_2__.useToast,\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__.useAuth\n    ];\n});\n_c = AppointmentModal;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (AppointmentModal);\nvar _c;\n$RefreshReg$(_c, \"AppointmentModal\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/calendar/AppointmentModal.js\n"));

/***/ })

});