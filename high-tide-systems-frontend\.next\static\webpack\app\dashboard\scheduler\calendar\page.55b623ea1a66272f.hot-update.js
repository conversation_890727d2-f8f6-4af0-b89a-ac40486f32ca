"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/scheduler/calendar/page",{

/***/ "(app-pages-browser)/./src/components/calendar/AppointmentModal.js":
/*!*****************************************************!*\
  !*** ./src/components/calendar/AppointmentModal.js ***!
  \*****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AppointmentModal: () => (/* binding */ AppointmentModal),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _contexts_ToastContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/ToastContext */ \"(app-pages-browser)/./src/contexts/ToastContext.js\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.js\");\n/* harmony import */ var _barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=format!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/format.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _components_ui_ModuleModal__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/ModuleModal */ \"(app-pages-browser)/./src/components/ui/ModuleModal.js\");\n/* harmony import */ var _app_modules_scheduler_services_appointmentService__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/app/modules/scheduler/services/appointmentService */ \"(app-pages-browser)/./src/app/modules/scheduler/services/appointmentService.js\");\n/* harmony import */ var _app_modules_scheduler_services_insuranceLimitService__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/app/modules/scheduler/services/insuranceLimitService */ \"(app-pages-browser)/./src/app/modules/scheduler/services/insuranceLimitService.js\");\n/* harmony import */ var _app_modules_scheduler_services_insuranceServiceLimitService__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/app/modules/scheduler/services/insuranceServiceLimitService */ \"(app-pages-browser)/./src/app/modules/scheduler/services/insuranceServiceLimitService.js\");\n/* harmony import */ var _app_modules_people_services_insurancesService__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/app/modules/people/services/insurancesService */ \"(app-pages-browser)/./src/app/modules/people/services/insurancesService.js\");\n/* harmony import */ var _components_ui_ConfirmationDialog__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/ConfirmationDialog */ \"(app-pages-browser)/./src/components/ui/ConfirmationDialog.js\");\n/* harmony import */ var _appointmentModal_BasicInfoForm__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./appointmentModal/BasicInfoForm */ \"(app-pages-browser)/./src/components/calendar/appointmentModal/BasicInfoForm.js\");\n/* harmony import */ var _appointmentModal_SpecialtySelector__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./appointmentModal/SpecialtySelector */ \"(app-pages-browser)/./src/components/calendar/appointmentModal/SpecialtySelector.js\");\n/* harmony import */ var _appointmentModal_AppointmentSelectors__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./appointmentModal/AppointmentSelectors */ \"(app-pages-browser)/./src/components/calendar/appointmentModal/AppointmentSelectors.js\");\n/* harmony import */ var _InsuranceLimitsDisplay__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ./InsuranceLimitsDisplay */ \"(app-pages-browser)/./src/components/calendar/InsuranceLimitsDisplay.js\");\n/* harmony import */ var _appointmentModal_AppointmentDates__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! ./appointmentModal/AppointmentDates */ \"(app-pages-browser)/./src/components/calendar/appointmentModal/AppointmentDates.js\");\n/* harmony import */ var _appointmentModal_AppointmentStatus__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! ./appointmentModal/AppointmentStatus */ \"(app-pages-browser)/./src/components/calendar/appointmentModal/AppointmentStatus.js\");\n/* harmony import */ var _appointmentModal_SequentialAppointments__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! ./appointmentModal/SequentialAppointments */ \"(app-pages-browser)/./src/components/calendar/appointmentModal/SequentialAppointments.js\");\n/* harmony import */ var _appointmentModal_RecurrenceSettings__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! ./appointmentModal/RecurrenceSettings */ \"(app-pages-browser)/./src/components/calendar/appointmentModal/RecurrenceSettings.js\");\n/* harmony import */ var _appointmentModal_ModuleModalFooter__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! ./appointmentModal/ModuleModalFooter */ \"(app-pages-browser)/./src/components/calendar/appointmentModal/ModuleModalFooter.js\");\n/* __next_internal_client_entry_do_not_use__ AppointmentModal,default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n// import { utcToLocal, localToUTC } from \"@/utils/dateFormatters\";\n\n\n\n\n\n\n\n// Importando os componentes refatorados\n\n\n\n\n\n\n\n\n\nconst AppointmentModal = (param)=>{\n    let { isOpen, onClose, selectedDate, selectedAppointment, onAppointmentChange, checkAvailability, canCreate = false, canEdit = false, canDelete = false } = param;\n    _s();\n    // Função para forçar o fechamento do modal\n    const forceCloseModal = ()=>{\n        // Resetar TODOS os estados que podem estar impedindo o fechamento\n        setConfirmationDialogOpen(false);\n        // Removido setCanCloseMainModal pois não existe mais\n        setIsLoading(false);\n        // Chamar onClose diretamente\n        onClose();\n    };\n    const { toast_success, toast_error } = (0,_contexts_ToastContext__WEBPACK_IMPORTED_MODULE_2__.useToast)();\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        title: \"\",\n        description: \"\",\n        specialty: \"\",\n        providerId: \"\",\n        personId: \"\",\n        locationId: \"\",\n        serviceTypeId: \"\",\n        insuranceId: \"\",\n        startDate: null,\n        endDate: null,\n        sequentialAppointments: 1,\n        status: \"PENDING\",\n        recurrence: {\n            enabled: false,\n            type: \"OCCURRENCES\",\n            numberOfOccurrences: 1,\n            endDate: null,\n            patterns: []\n        }\n    });\n    const [providers, setProviders] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [filteredProviders, setFilteredProviders] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [specialties, setSpecialties] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [persons, setPersons] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [locations, setLocations] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [serviceTypes, setServiceTypes] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [personInsurances, setPersonInsurances] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoadingInsurances, setIsLoadingInsurances] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isCheckingLimits, setIsCheckingLimits] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [limitInfo, setLimitInfo] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [confirmationDialogOpen, setConfirmationDialogOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [actionToConfirm, setActionToConfirm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Removido canCloseMainModal pois não é mais necessário\n    // Efeito para pré-carregar dados quando o modal é aberto com um agendamento existente\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AppointmentModal.useEffect\": ()=>{\n            if (isOpen && selectedAppointment) {\n                console.log(\"Modal aberto com agendamento existente, pré-carregando dados\");\n                console.log(\"Dados completos do agendamento selecionado:\", selectedAppointment);\n                // Verificar se o ID está definido\n                if (!selectedAppointment.id || selectedAppointment.id === \"undefined\") {\n                    console.error(\"ERRO: ID do agendamento inválido!\", selectedAppointment.id);\n                    return;\n                }\n                // Verificar se temos o personId\n                if (!selectedAppointment.personId) {\n                    console.warn(\"ALERTA: personId não encontrado no agendamento selecionado!\");\n                    // Não temos personId, mas não vamos buscar da API novamente\n                    // Apenas exibir um aviso e continuar com os dados que temos\n                    console.log(\"Continuando com os dados disponíveis sem personId\");\n                }\n                // Carregar convênios para o paciente se tivermos personId\n                if (selectedAppointment.personId) {\n                    loadPersonInsurances(selectedAppointment.personId, selectedAppointment.insuranceId);\n                    // Carregar tipos de serviço se tivermos convênio\n                    if (selectedAppointment.insuranceId) {\n                        loadServiceTypes(selectedAppointment.personId, selectedAppointment.insuranceId, selectedAppointment.serviceTypeId);\n                    }\n                }\n            }\n        }\n    }[\"AppointmentModal.useEffect\"], [\n        isOpen,\n        selectedAppointment\n    ]);\n    // Função para carregar convênios do paciente\n    const loadPersonInsurances = (personId, insuranceId)=>{\n        if (!personId) return;\n        console.log(\"Carregando conv\\xeanios para o paciente ID: \".concat(personId));\n        setIsLoadingInsurances(true);\n        _app_modules_people_services_insurancesService__WEBPACK_IMPORTED_MODULE_8__[\"default\"].listPersonInsurances(personId).then((insurances)=>{\n            console.log(\"Pr\\xe9-carregados \".concat((insurances === null || insurances === void 0 ? void 0 : insurances.length) || 0, \" conv\\xeanios para o paciente \").concat(personId));\n            setPersonInsurances(insurances || []);\n            // Verificar se o convênio do agendamento está na lista\n            if (insuranceId && insurances && insurances.length > 0) {\n                const insuranceExists = insurances.some((ins)=>(ins.insuranceId || ins.id) === insuranceId);\n                if (!insuranceExists) {\n                    console.log(\"Conv\\xeanio \".concat(insuranceId, \" n\\xe3o encontrado na lista, adicionando manualmente\"));\n                    // Adicionar o convênio manualmente à lista\n                    if (selectedAppointment.insurance) {\n                        const manualInsurance = {\n                            id: insuranceId,\n                            insuranceId: insuranceId,\n                            name: selectedAppointment.insurance.name || \"Convênio\",\n                            insurance: selectedAppointment.insurance\n                        };\n                        setPersonInsurances((prev)=>[\n                                ...prev,\n                                manualInsurance\n                            ]);\n                    } else {\n                        // Tentar buscar o convênio do backend\n                        _app_modules_people_services_insurancesService__WEBPACK_IMPORTED_MODULE_8__[\"default\"].getInsuranceById(insuranceId).then((insuranceData)=>{\n                            if (insuranceData) {\n                                const manualInsurance = {\n                                    id: insuranceId,\n                                    insuranceId: insuranceId,\n                                    name: insuranceData.name || \"Convênio\",\n                                    insurance: insuranceData\n                                };\n                                setPersonInsurances((prev)=>[\n                                        ...prev,\n                                        manualInsurance\n                                    ]);\n                            }\n                        }).catch((error)=>{\n                            console.error(\"Erro ao buscar convênio do backend:\", error);\n                        });\n                    }\n                }\n            }\n        }).catch((error)=>{\n            console.error(\"Erro ao pré-carregar convênios:\", error);\n        }).finally(()=>{\n            setIsLoadingInsurances(false);\n        });\n    };\n    // Função para carregar tipos de serviço\n    const loadServiceTypes = (personId, insuranceId, serviceTypeId)=>{\n        if (!personId || !insuranceId) return;\n        console.log(\"Carregando tipos de servi\\xe7o para o paciente ID: \".concat(personId, \" e conv\\xeanio ID: \").concat(insuranceId));\n        setIsLoading(true);\n        _app_modules_scheduler_services_insuranceServiceLimitService__WEBPACK_IMPORTED_MODULE_7__[\"default\"].getServiceTypesByInsurance(insuranceId, personId).then((serviceTypes)=>{\n            console.log(\"Pr\\xe9-carregados \".concat((serviceTypes === null || serviceTypes === void 0 ? void 0 : serviceTypes.length) || 0, \" tipos de servi\\xe7o\"));\n            setServiceTypes(serviceTypes || []);\n            // Verificar se o tipo de serviço do agendamento está na lista\n            if (serviceTypeId && serviceTypes && serviceTypes.length > 0) {\n                const serviceTypeExists = serviceTypes.some((type)=>type.id === serviceTypeId);\n                if (!serviceTypeExists) {\n                    console.log(\"Tipo de servi\\xe7o \".concat(serviceTypeId, \" n\\xe3o encontrado na lista, adicionando manualmente\"));\n                    // Adicionar o tipo de serviço manualmente à lista\n                    if (selectedAppointment.serviceType) {\n                        const manualServiceType = {\n                            id: serviceTypeId,\n                            name: selectedAppointment.serviceType.name || \"Tipo de Serviço\",\n                            value: selectedAppointment.serviceType.value || \"0\"\n                        };\n                        setServiceTypes((prev)=>[\n                                ...prev,\n                                manualServiceType\n                            ]);\n                    } else {\n                        // Tentar buscar o tipo de serviço do backend\n                        _app_modules_scheduler_services_insuranceServiceLimitService__WEBPACK_IMPORTED_MODULE_7__[\"default\"].getServiceTypeById(serviceTypeId).then((serviceTypeData)=>{\n                            if (serviceTypeData) {\n                                const manualServiceType = {\n                                    id: serviceTypeId,\n                                    name: serviceTypeData.name || \"Tipo de Serviço\",\n                                    value: serviceTypeData.value || \"0\"\n                                };\n                                setServiceTypes((prev)=>[\n                                        ...prev,\n                                        manualServiceType\n                                    ]);\n                            }\n                        }).catch((error)=>{\n                            console.error(\"Erro ao buscar tipo de serviço do backend:\", error);\n                        });\n                    }\n                }\n            }\n        }).catch((error)=>{\n            console.error(\"Erro ao pré-carregar tipos de serviço:\", error);\n        }).finally(()=>{\n            setIsLoading(false);\n        });\n    };\n    // Garantir que o modal possa ser fechado quando isOpen mudar para false\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AppointmentModal.useEffect\": ()=>{\n            if (!isOpen) {\n                // Resetar TODOS os estados que podem estar impedindo o fechamento\n                setConfirmationDialogOpen(false);\n                setIsLoading(false);\n                // Limpar formulário\n                setFormData({\n                    title: \"\",\n                    description: \"\",\n                    specialty: \"\",\n                    providerId: \"\",\n                    personId: \"\",\n                    locationId: \"\",\n                    serviceTypeId: \"\",\n                    insuranceId: \"\",\n                    startDate: null,\n                    endDate: null,\n                    sequentialAppointments: 1,\n                    status: \"PENDING\",\n                    recurrence: {\n                        enabled: false,\n                        type: \"OCCURRENCES\",\n                        numberOfOccurrences: 1,\n                        endDate: null,\n                        patterns: []\n                    }\n                });\n            }\n        }\n    }[\"AppointmentModal.useEffect\"], [\n        isOpen\n    ]);\n    // Verificar se o modal deve exibir modo de edição ou criação\n    const isEditMode = !!selectedAppointment;\n    // Verificar se o usuário tem permissão para a ação atual\n    const hasPermission = isEditMode ? canEdit : canCreate;\n    // Verificar se o usuário é um cliente\n    const { user } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    const isClient = user === null || user === void 0 ? void 0 : user.isClient;\n    // Modo somente leitura para clientes\n    const isReadOnlyMode = isClient && isEditMode;\n    // Se não tiver permissão e não for cliente em modo somente leitura, exibir mensagem\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AppointmentModal.useEffect\": ()=>{\n            if (isOpen && !hasPermission && !isReadOnlyMode) {\n                toast_error(isEditMode ? \"Você não tem permissão para editar agendamentos.\" : \"Você não tem permissão para criar agendamentos.\");\n            }\n        }\n    }[\"AppointmentModal.useEffect\"], [\n        isOpen,\n        hasPermission,\n        isEditMode,\n        isReadOnlyMode,\n        toast_error\n    ]);\n    // Função para extrair especialidades únicas dos provedores\n    const extractSpecialties = (providersList)=>{\n        const uniqueSpecialties = [\n            ...new Set(providersList.filter((provider)=>{\n                var _provider_professionObj;\n                return provider && (provider.profession || ((_provider_professionObj = provider.professionObj) === null || _provider_professionObj === void 0 ? void 0 : _provider_professionObj.name));\n            }).map((provider)=>{\n                var _provider_professionObj;\n                return provider.profession || ((_provider_professionObj = provider.professionObj) === null || _provider_professionObj === void 0 ? void 0 : _provider_professionObj.name);\n            }))\n        ].sort();\n        return uniqueSpecialties;\n    };\n    // Filtrar provedores baseado na especialidade selecionada\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AppointmentModal.useEffect\": ()=>{\n            if (formData.specialty) {\n                const filtered = providers.filter({\n                    \"AppointmentModal.useEffect.filtered\": (provider)=>{\n                        var _provider_professionObj;\n                        const providerProfession = provider.profession || ((_provider_professionObj = provider.professionObj) === null || _provider_professionObj === void 0 ? void 0 : _provider_professionObj.name);\n                        return providerProfession === formData.specialty;\n                    }\n                }[\"AppointmentModal.useEffect.filtered\"]);\n                setFilteredProviders(filtered);\n                // Se o provedor atualmente selecionado não estiver nesta especialidade, limpe a seleção\n                if (formData.providerId && !filtered.some({\n                    \"AppointmentModal.useEffect\": (p)=>p.id === formData.providerId\n                }[\"AppointmentModal.useEffect\"])) {\n                    setFormData({\n                        \"AppointmentModal.useEffect\": (prev)=>({\n                                ...prev,\n                                providerId: \"\"\n                            })\n                    }[\"AppointmentModal.useEffect\"]);\n                }\n            } else {\n                setFilteredProviders(providers);\n            }\n        }\n    }[\"AppointmentModal.useEffect\"], [\n        formData.specialty,\n        providers,\n        formData.providerId\n    ]);\n    // Carregar dados para os selects\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AppointmentModal.useEffect\": ()=>{\n            const loadData = {\n                \"AppointmentModal.useEffect.loadData\": async ()=>{\n                    setIsLoading(true);\n                    try {\n                        console.log(\"Loading appointment modal data...\");\n                        // Load data separately to better debug any issues\n                        console.log(\"Loading providers...\");\n                        const providersRes = await _app_modules_scheduler_services_appointmentService__WEBPACK_IMPORTED_MODULE_5__.appointmentService.getProviders();\n                        console.log(\"Loaded \".concat((providersRes === null || providersRes === void 0 ? void 0 : providersRes.length) || 0, \" providers\"));\n                        console.log(\"Loading persons...\");\n                        const personsRes = await _app_modules_scheduler_services_appointmentService__WEBPACK_IMPORTED_MODULE_5__.appointmentService.getPersons();\n                        console.log(\"Loaded \".concat((personsRes === null || personsRes === void 0 ? void 0 : personsRes.length) || 0, \" persons\"));\n                        console.log(\"Loading locations...\");\n                        const locationsRes = await _app_modules_scheduler_services_appointmentService__WEBPACK_IMPORTED_MODULE_5__.appointmentService.getLocations();\n                        console.log(\"Loaded \".concat((locationsRes === null || locationsRes === void 0 ? void 0 : locationsRes.length) || 0, \" locations\"));\n                        // Set state with valid data only\n                        const validProviders = providersRes || [];\n                        setProviders(validProviders);\n                        setFilteredProviders(validProviders);\n                        setPersons(personsRes || []);\n                        // Extract and set unique specialties\n                        const extractedSpecialties = extractSpecialties(validProviders);\n                        setSpecialties(extractedSpecialties);\n                        setLocations(locationsRes || []);\n                        // Log what was set\n                        console.log(\"Set providers:\", (validProviders === null || validProviders === void 0 ? void 0 : validProviders.length) || 0);\n                        console.log(\"Set specialties:\", (extractedSpecialties === null || extractedSpecialties === void 0 ? void 0 : extractedSpecialties.length) || 0);\n                        console.log(\"Set persons:\", (personsRes === null || personsRes === void 0 ? void 0 : personsRes.length) || 0);\n                        console.log(\"Set locations:\", (locationsRes === null || locationsRes === void 0 ? void 0 : locationsRes.length) || 0);\n                    } catch (error) {\n                        console.error(\"Erro ao carregar dados:\", error);\n                        toast_error(\"Erro ao carregar dados necessários. Por favor, tente novamente.\");\n                    } finally{\n                        setIsLoading(false);\n                    }\n                }\n            }[\"AppointmentModal.useEffect.loadData\"];\n            if (isOpen) {\n                loadData();\n            }\n        }\n    }[\"AppointmentModal.useEffect\"], [\n        isOpen\n    ]);\n    // Carregar os convênios do paciente selecionado\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AppointmentModal.useEffect\": ()=>{\n            const loadPersonInsurances = {\n                \"AppointmentModal.useEffect.loadPersonInsurances\": async ()=>{\n                    // Se não tiver um personId (paciente) selecionado, não fazer nada\n                    if (!formData.personId) {\n                        setPersonInsurances([]);\n                        return;\n                    }\n                    setIsLoadingInsurances(true);\n                    try {\n                        console.log(\"Carregando conv\\xeanios do paciente ID: \".concat(formData.personId));\n                        const insurancesRes = await _app_modules_people_services_insurancesService__WEBPACK_IMPORTED_MODULE_8__[\"default\"].listPersonInsurances(formData.personId);\n                        if (insurancesRes && insurancesRes.length > 0) {\n                            console.log(\"Carregados \".concat(insurancesRes.length, \" conv\\xeanios do paciente\"));\n                            console.log(\"Primeiro convênio:\", insurancesRes[0]);\n                            setPersonInsurances(insurancesRes);\n                            // Se estamos editando um agendamento existente e o convênio não está na lista,\n                            // não limpar o convênio selecionado\n                            if (formData.id && formData.insuranceId) {\n                                const insuranceExists = insurancesRes.some({\n                                    \"AppointmentModal.useEffect.loadPersonInsurances.insuranceExists\": (ins)=>(ins.insuranceId || ins.id) === formData.insuranceId\n                                }[\"AppointmentModal.useEffect.loadPersonInsurances.insuranceExists\"]);\n                                if (!insuranceExists) {\n                                    console.log(\"Conv\\xeanio \".concat(formData.insuranceId, \" n\\xe3o encontrado na lista, mas mantendo-o selecionado para edi\\xe7\\xe3o\"));\n                                }\n                            }\n                        // Removido o reset do convênio para novos agendamentos\n                        } else {\n                            console.warn(\"Nenhum convênio encontrado para este paciente\");\n                        // Manter o convênio selecionado mesmo se não houver convênios disponíveis\n                        // Isso permite que o usuário selecione \"Sem convênio\" manualmente\n                        }\n                    } catch (error) {\n                        console.error(\"Erro ao carregar conv\\xeanios do paciente \".concat(formData.personId, \":\"), error);\n                    } finally{\n                        setIsLoadingInsurances(false);\n                    }\n                }\n            }[\"AppointmentModal.useEffect.loadPersonInsurances\"];\n            // Carregar os convênios sempre que o paciente mudar\n            loadPersonInsurances();\n        }\n    }[\"AppointmentModal.useEffect\"], [\n        formData.personId,\n        formData.id,\n        formData.insuranceId\n    ]);\n    // Preencher dados do formulário baseado no evento selecionado ou data\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AppointmentModal.useEffect\": ()=>{\n            if (selectedAppointment) {\n                var _provider_professionObj;\n                console.log(\"Selected appointment:\", selectedAppointment);\n                // Verificar se o ID está definido\n                if (!selectedAppointment.id) {\n                    console.error(\"ERRO: ID do agendamento não definido!\");\n                    return;\n                }\n                // Buscar especialidade do provedor selecionado\n                const provider = providers.find({\n                    \"AppointmentModal.useEffect.provider\": (p)=>p.id === selectedAppointment.providerId\n                }[\"AppointmentModal.useEffect.provider\"]);\n                const providerSpecialty = (provider === null || provider === void 0 ? void 0 : provider.profession) || (provider === null || provider === void 0 ? void 0 : (_provider_professionObj = provider.professionObj) === null || _provider_professionObj === void 0 ? void 0 : _provider_professionObj.name) || \"\";\n                // Edição de agendamento existente\n                // Usar setTimeout para garantir que este efeito seja executado após os outros efeitos\n                // que podem estar tentando limpar os dados\n                setTimeout({\n                    \"AppointmentModal.useEffect\": ()=>{\n                        console.log(\"Preenchendo formulário com dados do agendamento selecionado\");\n                        console.log(\"Dados do agendamento:\", {\n                            id: selectedAppointment.id,\n                            personId: selectedAppointment.personId,\n                            insuranceId: selectedAppointment.insuranceId,\n                            serviceTypeId: selectedAppointment.serviceTypeId\n                        });\n                        // Garantir que temos valores válidos para todos os campos\n                        const formDataValues = {\n                            id: selectedAppointment.id,\n                            title: selectedAppointment.title || \"\",\n                            description: selectedAppointment.description || \"\",\n                            specialty: providerSpecialty || \"\",\n                            providerId: selectedAppointment.providerId || \"\",\n                            personId: selectedAppointment.personId || \"\",\n                            locationId: selectedAppointment.locationId || \"\",\n                            serviceTypeId: selectedAppointment.serviceTypeId || \"\",\n                            insuranceId: selectedAppointment.insuranceId || \"\",\n                            startDate: selectedAppointment.startDate || new Date().toISOString(),\n                            endDate: selectedAppointment.endDate || new Date(Date.now() + 3600000).toISOString(),\n                            sequentialAppointments: 1,\n                            status: selectedAppointment.status || \"PENDING\",\n                            recurrence: {\n                                enabled: false,\n                                type: \"OCCURRENCES\",\n                                numberOfOccurrences: 1,\n                                endDate: null,\n                                patterns: []\n                            }\n                        };\n                        console.log(\"Valores finais do formulário:\", formDataValues);\n                        setFormData(formDataValues);\n                    }\n                }[\"AppointmentModal.useEffect\"], 100); // Pequeno delay para garantir a ordem correta de execução\n            } else if (selectedDate) {\n                var _selectedDate_start, _selectedDate_end;\n                // Novo agendamento\n                // Usar os objetos Date diretamente, mantendo o horário local que o usuário clicou\n                console.log(\"[MODAL-DEBUG] selectedDate recebido:\", {\n                    start: (_selectedDate_start = selectedDate.start) === null || _selectedDate_start === void 0 ? void 0 : _selectedDate_start.toLocaleString(),\n                    end: (_selectedDate_end = selectedDate.end) === null || _selectedDate_end === void 0 ? void 0 : _selectedDate_end.toLocaleString(),\n                    temEnd: !!selectedDate.end,\n                    tipo: typeof selectedDate\n                });\n                // Para a data de início, usar o objeto Date do calendário diretamente\n                const startDate = selectedDate.start;\n                // Para a data de término, SEMPRE usar duração de 1 hora, ignorando o valor de end do calendário\n                console.log(\"[MODAL-DEBUG] FOR\\xc7ANDO endDate com dura\\xe7\\xe3o de 60 minutos, ignorando o valor de end\");\n                const endDate = new Date(selectedDate.start.getTime() + 60 * 60 * 1000);\n                console.log(\"[MODAL-DEBUG] Dura\\xe7\\xe3o for\\xe7ada (minutos): \".concat((endDate - startDate) / (60 * 1000)));\n                console.log(\"[MODAL-INIT] Datas do calend\\xe1rio (hor\\xe1rio local) - Start: \".concat(startDate.toLocaleString(), \", End: \").concat(endDate.toLocaleString()));\n                // Converter para strings ISO apenas para armazenamento no formData\n                // Importante: toISOString() converte para UTC, mas vamos usar isso apenas para armazenamento interno\n                const startISOString = startDate.toISOString();\n                const endISOString = endDate.toISOString();\n                setFormData({\n                    title: \"\",\n                    description: \"\",\n                    specialty: \"\",\n                    providerId: \"\",\n                    personId: \"\",\n                    locationId: \"\",\n                    serviceTypeId: \"\",\n                    insuranceId: \"\",\n                    startDate: startISOString,\n                    endDate: endISOString,\n                    sequentialAppointments: 1,\n                    status: \"PENDING\",\n                    recurrence: {\n                        enabled: false,\n                        type: \"OCCURRENCES\",\n                        numberOfOccurrences: 1,\n                        endDate: null,\n                        patterns: []\n                    }\n                });\n            }\n        }\n    }[\"AppointmentModal.useEffect\"], [\n        selectedAppointment,\n        selectedDate,\n        providers\n    ]);\n    // Carregar tipos de serviço com base no convênio selecionado ou todos os tipos se não houver convênio\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AppointmentModal.useEffect\": ()=>{\n            const loadServiceTypes = {\n                \"AppointmentModal.useEffect.loadServiceTypes\": async ()=>{\n                    // Se não tiver paciente, limpar tipos de serviço e retornar\n                    if (!formData.personId) {\n                        setServiceTypes([]);\n                        // Se não estamos editando um agendamento existente, resetar apenas o tipo de serviço\n                        if (!formData.id && formData.serviceTypeId) {\n                            setFormData({\n                                \"AppointmentModal.useEffect.loadServiceTypes\": (prev)=>({\n                                        ...prev,\n                                        serviceTypeId: \"\"\n                                    })\n                            }[\"AppointmentModal.useEffect.loadServiceTypes\"]);\n                        }\n                        return;\n                    }\n                    setIsLoading(true);\n                    try {\n                        let serviceTypesData = [];\n                        if (formData.insuranceId) {\n                            // Se há convênio, buscar tipos de serviço com limites configurados\n                            console.log(\"Carregando tipos de servi\\xe7o para o paciente ID: \".concat(formData.personId, \" e conv\\xeanio ID: \").concat(formData.insuranceId));\n                            serviceTypesData = await _app_modules_scheduler_services_insuranceServiceLimitService__WEBPACK_IMPORTED_MODULE_7__[\"default\"].getServiceTypesByInsurance(formData.insuranceId, formData.personId);\n                            console.log(\"Encontrados \".concat((serviceTypesData === null || serviceTypesData === void 0 ? void 0 : serviceTypesData.length) || 0, \" tipos de servi\\xe7o com limites configurados\"));\n                        } else {\n                            // Se não há convênio (agendamento particular), buscar todos os tipos de serviço da empresa\n                            console.log(\"Carregando todos os tipos de servi\\xe7o para agendamento particular\");\n                            // Importar o serviço de tipos de serviço\n                            const { serviceTypeService } = await __webpack_require__.e(/*! import() */ \"_app-pages-browser_src_app_modules_scheduler_services_serviceTypeService_js\").then(__webpack_require__.bind(__webpack_require__, /*! @/app/modules/scheduler/services/serviceTypeService */ \"(app-pages-browser)/./src/app/modules/scheduler/services/serviceTypeService.js\"));\n                            const response = await serviceTypeService.getServiceTypes();\n                            serviceTypesData = response.serviceTypes || response.data || [];\n                            console.log(\"Encontrados \".concat((serviceTypesData === null || serviceTypesData === void 0 ? void 0 : serviceTypesData.length) || 0, \" tipos de servi\\xe7o dispon\\xedveis\"));\n                        }\n                        if (serviceTypesData && serviceTypesData.length > 0) {\n                            setServiceTypes(serviceTypesData);\n                            // Se estamos editando um agendamento existente e o tipo de serviço não está na lista,\n                            // adicionar o tipo de serviço atual à lista\n                            if (formData.id && formData.serviceTypeId) {\n                                const serviceTypeExists = serviceTypesData.some({\n                                    \"AppointmentModal.useEffect.loadServiceTypes.serviceTypeExists\": (type)=>type.id === formData.serviceTypeId\n                                }[\"AppointmentModal.useEffect.loadServiceTypes.serviceTypeExists\"]);\n                                if (!serviceTypeExists && (selectedAppointment === null || selectedAppointment === void 0 ? void 0 : selectedAppointment.serviceType)) {\n                                    console.log(\"Tipo de servi\\xe7o \".concat(formData.serviceTypeId, \" n\\xe3o encontrado na lista, adicionando \\xe0 lista\"));\n                                    const currentServiceType = {\n                                        id: formData.serviceTypeId,\n                                        name: selectedAppointment.serviceType.name || \"Tipo de Serviço\",\n                                        value: selectedAppointment.serviceType.value || \"0\"\n                                    };\n                                    setServiceTypes({\n                                        \"AppointmentModal.useEffect.loadServiceTypes\": (prev)=>[\n                                                ...prev,\n                                                currentServiceType\n                                            ]\n                                    }[\"AppointmentModal.useEffect.loadServiceTypes\"]);\n                                }\n                            }\n                        } else {\n                            console.warn(\"Nenhum tipo de serviço encontrado\");\n                            setServiceTypes([]);\n                            // Se não estamos editando e temos um tipo de serviço selecionado, limpar apenas o tipo de serviço\n                            if (!formData.id && formData.serviceTypeId) {\n                                setFormData({\n                                    \"AppointmentModal.useEffect.loadServiceTypes\": (prev)=>({\n                                            ...prev,\n                                            serviceTypeId: \"\"\n                                        })\n                                }[\"AppointmentModal.useEffect.loadServiceTypes\"]);\n                            }\n                            // Exibir mensagem apenas se houver convênio mas não houver tipos de serviço\n                            if (formData.insuranceId) {\n                                toast_error(\"Não há tipos de serviço configurados para este paciente e convênio. \" + \"Por favor, configure os limites de serviço para esta combinação de paciente e convênio.\");\n                            }\n                        }\n                    } catch (error) {\n                        console.error(\"Erro ao carregar tipos de serviço:\", error);\n                        setServiceTypes([]);\n                        toast_error(\"Erro ao carregar tipos de serviço. Por favor, tente novamente.\");\n                    } finally{\n                        setIsLoading(false);\n                    }\n                }\n            }[\"AppointmentModal.useEffect.loadServiceTypes\"];\n            loadServiceTypes();\n        }\n    }[\"AppointmentModal.useEffect\"], [\n        formData.insuranceId,\n        formData.personId,\n        formData.id,\n        formData.serviceTypeId,\n        toast_error\n    ]);\n    // Verificar limites de convênio\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AppointmentModal.useEffect\": ()=>{\n            const checkInsuranceLimits = {\n                \"AppointmentModal.useEffect.checkInsuranceLimits\": async ()=>{\n                    // Só verifica se tiver pessoa, convênio e tipo de serviço selecionados\n                    if (formData.personId && formData.insuranceId && formData.serviceTypeId && formData.startDate) {\n                        setIsCheckingLimits(true);\n                        try {\n                            console.log(\"Verificando limites para data do agendamento: \".concat(formData.startDate));\n                            const result = await _app_modules_scheduler_services_insuranceLimitService__WEBPACK_IMPORTED_MODULE_6__[\"default\"].checkLimitAvailability({\n                                personId: formData.personId,\n                                insuranceId: formData.insuranceId,\n                                serviceTypeId: formData.serviceTypeId,\n                                appointmentDate: new Date(formData.startDate) // Passar a data do agendamento\n                            });\n                            console.log(\"Resultado da verifica\\xe7\\xe3o de limites:\", result);\n                            setLimitInfo(result.usage);\n                            // Se limite foi atingido, mostrar erro apenas para novos agendamentos\n                            // Para edição, apenas mostrar um aviso informativo\n                            if (!result.available) {\n                                if (!isEditMode) {\n                                    toast_error(result.message);\n                                } else {\n                                    // Para edição, mostrar um aviso mais suave\n                                    console.log(\"Limite atingido, mas permitindo edição do agendamento existente\");\n                                }\n                            }\n                        } catch (error) {\n                            console.error(\"Erro ao verificar limites:\", error);\n                        } finally{\n                            setIsCheckingLimits(false);\n                        }\n                    } else {\n                        // Limpar dados de limite se algum campo necessário não estiver preenchido\n                        setLimitInfo(null);\n                    }\n                }\n            }[\"AppointmentModal.useEffect.checkInsuranceLimits\"];\n            checkInsuranceLimits();\n        }\n    }[\"AppointmentModal.useEffect\"], [\n        formData.personId,\n        formData.insuranceId,\n        formData.serviceTypeId,\n        formData.startDate,\n        toast_error,\n        isEditMode\n    ]);\n    // Validar agendamento antes de enviar\n    const validateAppointment = async function() {\n        let data = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : formData;\n        // Validação básica\n        if (!data.title) {\n            toast_error(\"Por favor, informe um título para o agendamento.\");\n            return false;\n        }\n        if (!data.providerId) {\n            toast_error(\"Por favor, selecione um profissional.\");\n            return false;\n        }\n        if (!data.personId) {\n            toast_error(\"Por favor, selecione um paciente.\");\n            return false;\n        }\n        if (!data.locationId) {\n            toast_error(\"Por favor, selecione um local.\");\n            return false;\n        }\n        if (!data.serviceTypeId) {\n            toast_error(\"Por favor, selecione um tipo de serviço.\");\n            return false;\n        }\n        // Verificar limites de convênio apenas para novos agendamentos\n        // Para edição, permitir mesmo que o limite tenha sido atingido\n        if (!isEditMode && limitInfo && limitInfo.monthly && !limitInfo.monthly.unlimited) {\n            const isLimitReached = limitInfo.monthly.used >= limitInfo.monthly.limit;\n            if (isLimitReached) {\n                console.log(\"Limite de convênio atingido, bloqueando criação de novo agendamento\");\n                toast_error(\"Limite mensal de \".concat(limitInfo.monthly.limit, \" agendamentos atingido. Voc\\xea j\\xe1 utilizou \").concat(limitInfo.monthly.used, \" agendamentos.\"));\n                return false;\n            }\n        }\n        // Verificar se há conflitos com agendamentos existentes\n        try {\n            // Verificar disponibilidade tanto para novos agendamentos quanto para edição\n            // Verificar se o horário está disponível para o profissional no servidor\n            const providerCheckResponse = await _app_modules_scheduler_services_appointmentService__WEBPACK_IMPORTED_MODULE_5__.appointmentService.checkAvailability({\n                providerId: data.providerId,\n                startDate: data.startDate,\n                endDate: data.endDate,\n                excludeId: data.id // Para edição, excluir o próprio agendamento\n            });\n            if (!providerCheckResponse.available) {\n                if (providerCheckResponse.conflict) {\n                    toast_error(\"Horário indisponível para o profissional selecionado.\");\n                } else {\n                    toast_error(\"Horário indisponível para o profissional.\");\n                }\n                return false;\n            }\n            // Verificar se o paciente já tem um agendamento no mesmo horário\n            const patientCheckResponse = await _app_modules_scheduler_services_appointmentService__WEBPACK_IMPORTED_MODULE_5__.appointmentService.checkPatientAvailability({\n                personId: data.personId,\n                startDate: data.startDate,\n                endDate: data.endDate,\n                excludeId: data.id // Para edição, excluir o próprio agendamento\n            });\n            if (!patientCheckResponse.available) {\n                if (patientCheckResponse.conflict) {\n                    toast_error(\"Paciente já possui um agendamento neste horário.\");\n                } else {\n                    toast_error(\"Horário indisponível para o paciente.\");\n                }\n                return false;\n            }\n        } catch (error) {\n            console.error(\"Erro ao verificar disponibilidade no servidor:\", error);\n        // Não bloquear o fluxo em caso de erro na verificação\n        }\n        // Validar recorrência, se estiver habilitada (apenas para novos agendamentos)\n        if (!isEditMode && data.recurrence.enabled) {\n            // Validar padrões de recorrência\n            if (!data.recurrence.patterns || data.recurrence.patterns.length === 0) {\n                toast_error(\"Por favor, selecione pelo menos um dia da semana para a recorrência.\");\n                return false;\n            }\n            // Validar data final ou número de ocorrências\n            if (data.recurrence.type === 'END_DATE') {\n                if (!data.recurrence.endDate) {\n                    toast_error(\"Por favor, informe uma data final para a recorrência.\");\n                    return false;\n                }\n                const endDate = new Date(data.recurrence.endDate);\n                const tomorrow = new Date();\n                tomorrow.setDate(tomorrow.getDate() + 1);\n                if (endDate <= tomorrow) {\n                    toast_error(\"A data final da recorrência deve ser pelo menos 1 dia após a data atual.\");\n                    return false;\n                }\n            } else if (data.recurrence.type === 'OCCURRENCES') {\n                if (!data.recurrence.numberOfOccurrences || data.recurrence.numberOfOccurrences < 1) {\n                    toast_error(\"Por favor, informe um número válido de ocorrências para a recorrência.\");\n                    return false;\n                }\n            }\n        }\n        // Verificar disponibilidade do profissional, se implementado\n        if (checkAvailability) {\n            const isAvailable = await checkAvailability(data.providerId, {\n                start: data.startDate,\n                end: data.endDate\n            });\n            if (!isAvailable) {\n                toast_error(\"O profissional selecionado não está disponível neste horário.\");\n                return false;\n            }\n            // Se estiver criando agendamentos sequenciais (apenas para novos agendamentos), verificar cada um deles\n            if (!isEditMode && data.sequentialAppointments > 1) {\n                // Converter strings ISO para objetos Date para cálculos\n                const startDate = new Date(data.startDate);\n                const endDate = new Date(data.endDate);\n                let currentEndDate = endDate;\n                // Duração fixa de 1 hora (3600000 ms) para agendamentos sequenciais\n                const sequentialDuration = 3600000; // 1 hora em milissegundos\n                console.log(\"[SEQUENTIAL] Verificando \".concat(data.sequentialAppointments, \" agendamentos sequenciais\"));\n                console.log(\"[SEQUENTIAL] Agendamento inicial: \".concat(startDate.toLocaleString(), \" - \").concat(endDate.toLocaleString()));\n                for(let i = 1; i < data.sequentialAppointments; i++){\n                    // Criar o próximo agendamento sequencial\n                    // Começa exatamente quando o anterior termina\n                    const nextStartDate = new Date(currentEndDate.getTime());\n                    // Duração fixa de 1 hora\n                    const nextEndDate = new Date(nextStartDate.getTime() + sequentialDuration);\n                    console.log(\"[SEQUENTIAL] Verificando agendamento #\".concat(i + 1, \": \").concat(nextStartDate.toLocaleString(), \" - \").concat(nextEndDate.toLocaleString()));\n                    // Converter para strings ISO para verificar disponibilidade\n                    const nextStartISO = nextStartDate.toISOString();\n                    const nextEndISO = nextEndDate.toISOString();\n                    // Verificar disponibilidade do profissional no servidor para este horário sequencial\n                    try {\n                        const providerServerCheckResponse = await _app_modules_scheduler_services_appointmentService__WEBPACK_IMPORTED_MODULE_5__.appointmentService.checkAvailability({\n                            providerId: data.providerId,\n                            startDate: nextStartISO,\n                            endDate: nextEndISO,\n                            excludeId: data.id // Para edição, excluir o próprio agendamento\n                        });\n                        // Se não estiver disponível no servidor, mostrar mensagem de erro\n                        if (!providerServerCheckResponse.available) {\n                            // Formatar as datas para exibição\n                            const formatTime = (date)=>(0,_barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_19__.format)(date, 'HH:mm');\n                            if (providerServerCheckResponse.conflict) {\n                                const conflict = providerServerCheckResponse.conflict;\n                                const conflictStart = new Date(conflict.startDate);\n                                const conflictEnd = new Date(conflict.endDate);\n                                const appointmentTitle = conflict.title || \"Sem título\";\n                                toast_error(\"O \".concat(i + 1, \"\\xba agendamento sequencial (\").concat(formatTime(nextStartDate), \" - \").concat(formatTime(nextEndDate), ') conflita com o agendamento \"').concat(appointmentTitle, '\" (').concat((0,_barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_19__.format)(conflictStart, 'HH:mm'), \" - \").concat((0,_barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_19__.format)(conflictEnd, 'HH:mm'), \").\"));\n                            } else {\n                                toast_error(\"O profissional n\\xe3o est\\xe1 dispon\\xedvel para o \".concat(i + 1, \"\\xba agendamento sequencial (\").concat(formatTime(nextStartDate), \" - \").concat(formatTime(nextEndDate), \").\"));\n                            }\n                            return false;\n                        }\n                        // Verificar disponibilidade do paciente no servidor para este horário sequencial\n                        const patientServerCheckResponse = await _app_modules_scheduler_services_appointmentService__WEBPACK_IMPORTED_MODULE_5__.appointmentService.checkPatientAvailability({\n                            personId: data.personId,\n                            startDate: nextStartISO,\n                            endDate: nextEndISO,\n                            excludeId: data.id // Para edição, excluir o próprio agendamento\n                        });\n                        // Se não estiver disponível no servidor, mostrar mensagem de erro\n                        if (!patientServerCheckResponse.available) {\n                            // Formatar as datas para exibição\n                            const formatTime = (date)=>(0,_barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_19__.format)(date, 'HH:mm');\n                            if (patientServerCheckResponse.conflict) {\n                                const conflict = patientServerCheckResponse.conflict;\n                                const conflictStart = new Date(conflict.startDate);\n                                const conflictEnd = new Date(conflict.endDate);\n                                const appointmentTitle = conflict.title || \"Sem título\";\n                                toast_error('O paciente j\\xe1 possui um agendamento \"'.concat(appointmentTitle, '\" (').concat((0,_barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_19__.format)(conflictStart, 'HH:mm'), \" - \").concat((0,_barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_19__.format)(conflictEnd, 'HH:mm'), \") que conflita com o \").concat(i + 1, \"\\xba agendamento sequencial (\").concat(formatTime(nextStartDate), \" - \").concat(formatTime(nextEndDate), \").\"));\n                            } else {\n                                toast_error(\"O paciente j\\xe1 possui um agendamento no hor\\xe1rio do \".concat(i + 1, \"\\xba agendamento sequencial (\").concat(formatTime(nextStartDate), \" - \").concat(formatTime(nextEndDate), \").\"));\n                            }\n                            return false;\n                        }\n                    } catch (error) {\n                        console.error(\"Erro ao verificar disponibilidade do \".concat(i + 1, \"\\xba agendamento sequencial:\"), error);\n                    // Continuar com a verificação local em caso de erro na verificação do servidor\n                    }\n                    // Verificar disponibilidade local (horários de trabalho)\n                    const isNextSlotAvailable = await checkAvailability(data.providerId, {\n                        start: nextStartISO,\n                        end: nextEndISO\n                    });\n                    if (!isNextSlotAvailable) {\n                        toast_error(\"O profissional n\\xe3o est\\xe1 dispon\\xedvel para o \".concat(i + 1, \"\\xba agendamento sequencial (\").concat((0,_barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_19__.format)(nextStartDate, 'HH:mm'), \" - \").concat((0,_barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_19__.format)(nextEndDate, 'HH:mm'), \").\"));\n                        return false;\n                    }\n                    currentEndDate = nextEndDate;\n                }\n            }\n        }\n        return true;\n    };\n    // Função para confirmar a ação antes de executar\n    const confirmAction = async ()=>{\n        if (!actionToConfirm) {\n            return;\n        }\n        console.log('[CONFIRM-ACTION] Confirmando ação:', actionToConfirm.type);\n        try {\n            // Fechar o diálogo de confirmação imediatamente\n            setConfirmationDialogOpen(false);\n            // Garantir que o modal principal possa ser fechado\n            if (actionToConfirm.type === \"save\") {\n                await saveAppointment();\n            } else if (actionToConfirm.type === \"delete\") {\n                await deleteAppointment();\n            }\n        } catch (error) {\n            console.error('[CONFIRM-ACTION] Erro ao executar ação:', error);\n        // Garantir que o modal possa ser fechado mesmo em caso de erro\n        } finally{\n            // Garantir que o modal possa ser fechado após a ação, independente do resultado\n            setTimeout(()=>{\n                console.log('[CONFIRM-ACTION] Garantindo que o modal possa ser fechado após a ação');\n            }, 500);\n        }\n    };\n    // Função para salvar o agendamento após confirmação\n    const saveAppointment = async ()=>{\n        setIsLoading(true);\n        try {\n            // As datas no formData estão em formato ISO string (UTC)\n            // Isso é exatamente o que o backend espera, então não precisamos converter\n            console.log(\"[SAVE] Datas originais (UTC) - Start: \".concat(formData.startDate, \", End: \").concat(formData.endDate));\n            // Converter para objetos Date para exibir no log o horário local\n            const startLocalDate = new Date(formData.startDate);\n            const endLocalDate = new Date(formData.endDate);\n            console.log(\"[SAVE] Datas locais - Start: \".concat(startLocalDate.toLocaleString(), \", End: \").concat(endLocalDate.toLocaleString()));\n            // Criar objeto base com dados comuns\n            const baseAppointmentData = {\n                title: formData.title,\n                description: formData.description,\n                userId: formData.providerId,\n                providerId: formData.providerId,\n                personId: formData.personId,\n                locationId: formData.locationId,\n                serviceTypeId: formData.serviceTypeId,\n                insuranceId: formData.insuranceId || null,\n                // Enviar as datas em formato ISO (UTC) para o backend\n                startDate: formData.startDate,\n                endDate: formData.endDate,\n                creatorId: formData.providerId,\n                status: formData.status\n            };\n            // Adicionar sequentialAppointments apenas para novos agendamentos\n            const appointmentData = isEditMode ? baseAppointmentData : {\n                ...baseAppointmentData,\n                sequentialAppointments: formData.sequentialAppointments\n            };\n            console.log(\"[SAVE] Enviando datas (UTC) - Start: \".concat(appointmentData.startDate, \", End: \").concat(appointmentData.endDate));\n            if (!isEditMode && formData.recurrence.enabled) {\n                // Preparar dados de recorrência\n                const recurrenceData = {\n                    ...appointmentData,\n                    recurrenceType: formData.recurrence.type,\n                    // Para o tipo OCCURRENCES, usar o número de ocorrências\n                    // Para o tipo END_DATE, usar a data final (já em formato ISO)\n                    recurrenceValue: formData.recurrence.type === \"OCCURRENCES\" ? formData.recurrence.numberOfOccurrences : formData.recurrence.endDate,\n                    // Mapear os padrões de recorrência\n                    patterns: formData.recurrence.patterns.map((pattern)=>{\n                        console.log(\"[RECURRENCE] Padr\\xe3o: dia \".concat(pattern.dayOfWeek, \", \").concat(pattern.startTime, \" - \").concat(pattern.endTime));\n                        return {\n                            dayOfWeek: pattern.dayOfWeek,\n                            startTime: pattern.startTime,\n                            endTime: pattern.endTime\n                        };\n                    })\n                };\n                console.log(\"[RECURRENCE] Criando recorr\\xeancia do tipo \".concat(recurrenceData.recurrenceType));\n                console.log(\"[RECURRENCE] Valor: \".concat(recurrenceData.recurrenceValue));\n                console.log(\"[RECURRENCE] Padr\\xf5es: \".concat(recurrenceData.patterns.length));\n                await _app_modules_scheduler_services_appointmentService__WEBPACK_IMPORTED_MODULE_5__.appointmentService.createRecurrence(recurrenceData);\n            } else {\n                if (formData.id) {\n                    // Verificar se o ID é válido antes de atualizar\n                    if (!formData.id || formData.id === \"undefined\") {\n                        console.error(\"[SAVE] ID do agendamento inválido para atualização:\", formData.id);\n                        throw new Error(\"ID do agendamento inválido. Não é possível atualizar.\");\n                    }\n                    console.log(\"[SAVE] Atualizando agendamento com ID:\", formData.id);\n                    try {\n                        // Update existente\n                        await _app_modules_scheduler_services_appointmentService__WEBPACK_IMPORTED_MODULE_5__.appointmentService.updateAppointment(formData.id, {\n                            ...appointmentData,\n                            updatedBy: formData.providerId\n                        });\n                    } catch (updateError) {\n                        console.error(\"[SAVE] Erro ao atualizar agendamento:\", updateError);\n                        // Verificar se o erro é 404 (agendamento não encontrado)\n                        if (updateError.response && updateError.response.status === 404) {\n                            console.log(\"[SAVE] Agendamento não encontrado, tentando criar um novo\");\n                            // Tentar criar um novo agendamento com os mesmos dados\n                            await _app_modules_scheduler_services_appointmentService__WEBPACK_IMPORTED_MODULE_5__.appointmentService.createAppointment(appointmentData);\n                        } else {\n                            // Se não for 404, propagar o erro\n                            throw updateError;\n                        }\n                    }\n                } else {\n                    // Novo agendamento\n                    await _app_modules_scheduler_services_appointmentService__WEBPACK_IMPORTED_MODULE_5__.appointmentService.createAppointment(appointmentData);\n                }\n            }\n            // Exibir mensagem de sucesso\n            let successMessage = \"\";\n            if (isEditMode) {\n                successMessage = \"Agendamento atualizado com sucesso!\";\n            } else if (formData.recurrence.enabled) {\n                successMessage = \"Recorrência criada com sucesso!\";\n            } else {\n                successMessage = \"Agendamento criado com sucesso!\";\n            }\n            toast_success({\n                title: \"Sucesso\",\n                message: successMessage\n            });\n            onAppointmentChange();\n            onClose(); // Fechar o modal de agendamento apenas após sucesso\n        } catch (error) {\n            console.error(\"Erro ao salvar agendamento:\", error);\n            // Extrair mensagem de erro da resposta da API, se disponível\n            let errorMsg = \"Erro ao salvar o agendamento. Por favor, tente novamente.\";\n            if (error.response && error.response.data) {\n                if (error.response.data.message) {\n                    errorMsg = error.response.data.message;\n                    // Adicionar informações mais detalhadas para conflitos de horário\n                    if (error.response.data.reason === \"CONFLICT\" && error.response.data.conflictData) {\n                        const conflict = error.response.data.conflictData;\n                        const conflictStart = new Date(conflict.startDate);\n                        const conflictEnd = new Date(conflict.endDate);\n                        // Formatar as datas para exibição\n                        const formatDate = (date)=>{\n                            try {\n                                if (!date) return \"horário não especificado\";\n                                // Verificar se a data é válida\n                                if (isNaN(date.getTime())) return \"horário não disponível\";\n                                return date.toLocaleTimeString([], {\n                                    hour: '2-digit',\n                                    minute: '2-digit'\n                                });\n                            } catch (error) {\n                                console.error(\"Erro ao formatar data:\", error, date);\n                                return \"horário não disponível\";\n                            }\n                        };\n                        const appointmentTitle = conflict.title || \"Sem título\";\n                        errorMsg += ' Existe um agendamento conflitante \"'.concat(appointmentTitle, '\" no per\\xedodo ').concat(formatDate(conflictStart), \" - \").concat(formatDate(conflictEnd), \".\");\n                    }\n                } else if (error.response.data.error) {\n                    errorMsg = error.response.data.error;\n                }\n            } else if (error.message) {\n                errorMsg = error.message;\n            }\n            // Exibir toast de erro\n            toast_error({\n                title: \"Erro ao salvar agendamento\",\n                message: errorMsg\n            });\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    // Função para excluir agendamento após confirmação\n    const deleteAppointment = async ()=>{\n        // Verificar se o ID é válido antes de excluir\n        if (!formData.id || formData.id === \"undefined\") {\n            console.error(\"[DELETE] ID do agendamento inválido para exclusão:\", formData.id);\n            toast_error(\"ID do agendamento inválido. Não é possível excluir.\");\n            return;\n        }\n        setIsLoading(true);\n        console.log(\"[DELETE] Excluindo agendamento com ID:\", formData.id);\n        try {\n            try {\n                await _app_modules_scheduler_services_appointmentService__WEBPACK_IMPORTED_MODULE_5__.appointmentService.deleteAppointment(formData.id);\n            } catch (deleteError) {\n                console.error(\"[DELETE] Erro ao excluir agendamento:\", deleteError);\n                // Verificar se o erro é 404 (agendamento não encontrado)\n                if (deleteError.response && deleteError.response.status === 404) {\n                    // Se o agendamento não existe, considerar como excluído com sucesso\n                    console.log(\"[DELETE] Agendamento não encontrado, considerando como excluído\");\n                // Não propagar o erro, continuar como se tivesse excluído com sucesso\n                } else {\n                    // Se não for 404, propagar o erro\n                    throw deleteError;\n                }\n            }\n            // Exibir mensagem de sucesso\n            toast_success({\n                title: \"Sucesso\",\n                message: \"Agendamento excluído com sucesso!\"\n            });\n            onAppointmentChange();\n            onClose(); // Fechar o modal de agendamento apenas após sucesso\n        } catch (error) {\n            console.error(\"Erro ao excluir agendamento:\", error);\n            // Extrair mensagem de erro da resposta da API, se disponível\n            let errorMsg = \"Erro ao excluir o agendamento. Por favor, tente novamente.\";\n            if (error.response && error.response.data) {\n                if (error.response.data.message) {\n                    errorMsg = error.response.data.message;\n                } else if (error.response.data.error) {\n                    errorMsg = error.response.data.error;\n                }\n            } else if (error.message) {\n                errorMsg = error.message;\n            }\n            // Exibir toast de erro\n            toast_error({\n                title: \"Erro ao excluir agendamento\",\n                message: errorMsg\n            });\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        // Verificar novamente a permissão antes de enviar\n        if (!hasPermission || isReadOnlyMode) {\n            toast_error(isEditMode ? \"Você não tem permissão para editar agendamentos.\" : \"Você não tem permissão para criar agendamentos.\");\n            return;\n        }\n        // Validar dados do agendamento\n        const isValid = await validateAppointment();\n        if (!isValid) {\n            return;\n        }\n        // Preparar mensagem de confirmação\n        let actionMessage = \"\";\n        let actionTitle = \"\";\n        if (isEditMode) {\n            // Mensagem para edição de agendamento existente\n            actionTitle = \"Atualizar agendamento\";\n            actionMessage = 'Deseja atualizar o agendamento \"'.concat(formData.title, '\"?');\n        } else if (formData.recurrence.enabled) {\n            // Mensagem para criação de agendamento recorrente\n            actionTitle = \"Criar agendamentos recorrentes\";\n            actionMessage = \"Deseja criar uma s\\xe9rie de agendamentos recorrentes para \".concat(formData.title, \"?\");\n        } else {\n            // Mensagem para criação de agendamento normal ou sequencial\n            actionTitle = formData.sequentialAppointments > 1 ? \"Criar agendamentos sequenciais\" : \"Criar agendamento\";\n            actionMessage = formData.sequentialAppointments > 1 ? \"Deseja criar \".concat(formData.sequentialAppointments, ' agendamentos sequenciais para \"').concat(formData.title, '\"?') : 'Deseja criar o agendamento \"'.concat(formData.title, '\"?');\n        }\n        // Configurar ação para confirmação\n        setActionToConfirm({\n            type: \"save\",\n            message: actionMessage,\n            title: actionTitle,\n            variant: \"info\"\n        });\n        // Abrir diálogo de confirmação\n        setConfirmationDialogOpen(true);\n    };\n    // Função para solicitar confirmação de exclusão\n    const handleDelete = ()=>{\n        // Verificar permissão de exclusão\n        if (!canDelete) {\n            toast_error(\"Você não tem permissão para excluir agendamentos.\");\n            return;\n        }\n        if (!formData.id) return;\n        // Configurar ação para confirmação\n        setActionToConfirm({\n            type: \"delete\",\n            message: 'Tem certeza que deseja excluir o agendamento \"'.concat(formData.title, '\"?'),\n            title: \"Excluir agendamento\",\n            variant: \"danger\"\n        });\n        // Abrir diálogo de confirmação\n        setConfirmationDialogOpen(true);\n    };\n    // Função para calcular o horário do último agendamento sequencial\n    const calculateLastAppointmentTime = ()=>{\n        if (formData.sequentialAppointments <= 1 || !formData.startDate || !formData.endDate) {\n            return null;\n        }\n        // Converter strings ISO para objetos Date para cálculos\n        const endDate = new Date(formData.endDate);\n        // Duração fixa de 1 hora (3600000 ms) para agendamentos sequenciais\n        const sequentialDuration = 3600000; // 1 hora em milissegundos\n        // Começar com o horário de término do primeiro agendamento\n        let currentEndTime = endDate;\n        // Calcular o horário do último agendamento\n        for(let i = 1; i < formData.sequentialAppointments; i++){\n            // O próximo agendamento começa quando o anterior termina\n            const nextStartTime = new Date(currentEndTime.getTime());\n            // Duração fixa de 1 hora\n            const nextEndTime = new Date(nextStartTime.getTime() + sequentialDuration);\n            // Atualizar para o próximo agendamento\n            currentEndTime = nextEndTime;\n        }\n        // O último agendamento começa 1 hora antes do horário final calculado\n        const lastStartTime = new Date(currentEndTime.getTime() - sequentialDuration);\n        const lastEndTime = currentEndTime;\n        console.log(\"[SEQUENTIAL-CALC] \\xdaltimo agendamento: \".concat(lastStartTime.toLocaleString(), \" - \").concat(lastEndTime.toLocaleString()));\n        // Retornar os objetos Date diretamente\n        return {\n            start: lastStartTime,\n            end: lastEndTime\n        };\n    };\n    // Calcular o último horário para os agendamentos sequenciais\n    // Usar useEffect para recalcular sempre que formData mudar\n    const [lastAppointmentTime, setLastAppointmentTime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AppointmentModal.useEffect\": ()=>{\n            setLastAppointmentTime(calculateLastAppointmentTime());\n        }\n    }[\"AppointmentModal.useEffect\"], [\n        formData.sequentialAppointments,\n        formData.startDate,\n        formData.endDate\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ConfirmationDialog__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                isOpen: confirmationDialogOpen,\n                onClose: ()=>{\n                    console.log('[CONFIRMATION-DIALOG] Fechando diálogo de confirmação');\n                    // Fechar o diálogo de confirmação\n                    setConfirmationDialogOpen(false);\n                },\n                onConfirm: confirmAction,\n                title: (actionToConfirm === null || actionToConfirm === void 0 ? void 0 : actionToConfirm.title) || \"Confirmar ação\",\n                message: (actionToConfirm === null || actionToConfirm === void 0 ? void 0 : actionToConfirm.message) || \"\",\n                variant: (actionToConfirm === null || actionToConfirm === void 0 ? void 0 : actionToConfirm.variant) || \"info\",\n                confirmText: (actionToConfirm === null || actionToConfirm === void 0 ? void 0 : actionToConfirm.type) === \"delete\" ? \"Excluir\" : \"Confirmar\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\calendar\\\\AppointmentModal.js\",\n                lineNumber: 1280,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ModuleModal__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                isOpen: isOpen,\n                onClose: ()=>{\n                    // Sempre usar forceCloseModal para garantir o fechamento\n                    forceCloseModal();\n                },\n                animateExit: false,\n                title: selectedAppointment ? \"Editar Agendamento\" : \"Novo Agendamento\",\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                    size: 22\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\calendar\\\\AppointmentModal.js\",\n                    lineNumber: 1302,\n                    columnNumber: 15\n                }, void 0),\n                moduleColor: \"scheduler\",\n                size: \"xl\",\n                preventClose: false,\n                onInteractOutside: ()=>{\n                    console.log('[OUTSIDE-CLICK] Clique fora do modal');\n                    // Sempre permitir o fechamento do modal\n                    forceCloseModal();\n                },\n                footer: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_appointmentModal_ModuleModalFooter__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                    isEditMode: isEditMode,\n                    canDelete: canDelete && !isReadOnlyMode,\n                    hasPermission: hasPermission && !isReadOnlyMode,\n                    formData: formData,\n                    isLoading: isLoading,\n                    limitInfo: limitInfo,\n                    handleDelete: handleDelete,\n                    onClose: ()=>{\n                        console.log('[FOOTER-CLOSE] Tentativa de fechar o modal via footer');\n                        forceCloseModal(); // Forçar fechamento independente do estado\n                    }\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\calendar\\\\AppointmentModal.js\",\n                    lineNumber: 1312,\n                    columnNumber: 11\n                }, void 0),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                    id: \"appointment-form\",\n                    onSubmit: handleSubmit,\n                    className: \"overflow-y-auto dark:bg-gray-800 flex flex-col justify-between p-5\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-5\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_appointmentModal_BasicInfoForm__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                            formData: formData,\n                                            setFormData: setFormData,\n                                            readOnly: isReadOnlyMode\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\calendar\\\\AppointmentModal.js\",\n                                            lineNumber: 1335,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_appointmentModal_SpecialtySelector__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                            formData: formData,\n                                            setFormData: setFormData,\n                                            specialties: specialties,\n                                            readOnly: isReadOnlyMode\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\calendar\\\\AppointmentModal.js\",\n                                            lineNumber: 1342,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_appointmentModal_AppointmentSelectors__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            formData: formData,\n                                            setFormData: setFormData,\n                                            filteredProviders: filteredProviders,\n                                            persons: persons,\n                                            locations: locations,\n                                            serviceTypes: serviceTypes,\n                                            personInsurances: personInsurances,\n                                            isLoadingInsurances: isLoadingInsurances,\n                                            isLoading: isLoading,\n                                            readOnly: isReadOnlyMode\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\calendar\\\\AppointmentModal.js\",\n                                            lineNumber: 1350,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\calendar\\\\AppointmentModal.js\",\n                                    lineNumber: 1333,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-5\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_appointmentModal_AppointmentDates__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                            formData: formData,\n                                            setFormData: setFormData,\n                                            readOnly: isReadOnlyMode\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\calendar\\\\AppointmentModal.js\",\n                                            lineNumber: 1366,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        isEditMode && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_appointmentModal_AppointmentStatus__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                            formData: formData,\n                                            setFormData: setFormData,\n                                            readOnly: isReadOnlyMode\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\calendar\\\\AppointmentModal.js\",\n                                            lineNumber: 1374,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        !isEditMode && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mb-8\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_appointmentModal_SequentialAppointments__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                formData: formData,\n                                                setFormData: setFormData,\n                                                lastAppointmentTime: lastAppointmentTime\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\calendar\\\\AppointmentModal.js\",\n                                                lineNumber: 1384,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\calendar\\\\AppointmentModal.js\",\n                                            lineNumber: 1383,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        !isEditMode && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_appointmentModal_RecurrenceSettings__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                            formData: formData,\n                                            setFormData: setFormData\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\calendar\\\\AppointmentModal.js\",\n                                            lineNumber: 1394,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        formData.personId && formData.insuranceId && formData.serviceTypeId && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_InsuranceLimitsDisplay__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                            limitInfo: limitInfo,\n                                            isLoading: isCheckingLimits,\n                                            isEditMode: isEditMode\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\calendar\\\\AppointmentModal.js\",\n                                            lineNumber: 1402,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\calendar\\\\AppointmentModal.js\",\n                                    lineNumber: 1364,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\calendar\\\\AppointmentModal.js\",\n                            lineNumber: 1332,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\calendar\\\\AppointmentModal.js\",\n                        lineNumber: 1331,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\calendar\\\\AppointmentModal.js\",\n                    lineNumber: 1328,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\calendar\\\\AppointmentModal.js\",\n                lineNumber: 1294,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true);\n};\n_s(AppointmentModal, \"ZoEZAigQHwIJpzhwwu4gfJhoH9Y=\", false, function() {\n    return [\n        _contexts_ToastContext__WEBPACK_IMPORTED_MODULE_2__.useToast,\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__.useAuth\n    ];\n});\n_c = AppointmentModal;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (AppointmentModal);\nvar _c;\n$RefreshReg$(_c, \"AppointmentModal\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/calendar/AppointmentModal.js\n"));

/***/ })

});