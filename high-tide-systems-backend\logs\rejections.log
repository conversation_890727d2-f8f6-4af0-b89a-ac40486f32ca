{"timestamp": "2025-06-18 15:53:39", "level": "error", "message": "unhandledRejection: The client is closed\nError: The client is closed\n    at RedisSocket.quit (/usr/src/app/node_modules/@redis/client/dist/lib/client/socket.js:70:19)\n    at Commander.QUIT (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:260:71)\n    at CacheService.close (/usr/src/app/src/services/cacheService.js:70:25)\n    at process.<anonymous> (/usr/src/app/src/server.js:305:22)\n    at process.emit (node:events:519:28)", "error": {}, "stack": "Error: The client is closed\n    at RedisSocket.quit (/usr/src/app/node_modules/@redis/client/dist/lib/client/socket.js:70:19)\n    at Commander.QUIT (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:260:71)\n    at CacheService.close (/usr/src/app/src/services/cacheService.js:70:25)\n    at process.<anonymous> (/usr/src/app/src/server.js:305:22)\n    at process.emit (node:events:519:28)", "rejection": true, "date": "Wed Jun 18 2025 15:53:39 GMT+0000 (Coordinated Universal Time)", "process": {"pid": 138, "uid": 0, "gid": 0, "cwd": "/usr/src/app", "execPath": "/usr/local/bin/node", "version": "v20.17.0", "argv": ["/usr/local/bin/node", "/usr/src/app/src/server.js", "src/server.js"], "memoryUsage": {"rss": 139132928, "heapTotal": 45232128, "heapUsed": 43293168, "external": 3406175, "arrayBuffers": 132102}}, "os": {"loadavg": [1.92, 1.6, 1.62], "uptime": 4462.7}, "trace": [{"column": 19, "file": "/usr/src/app/node_modules/@redis/client/dist/lib/client/socket.js", "function": "RedisSocket.quit", "line": 70, "method": "quit", "native": false}, {"column": 71, "file": "/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js", "function": "Commander.QUIT", "line": 260, "method": "QUIT", "native": false}, {"column": 25, "file": "/usr/src/app/src/services/cacheService.js", "function": "CacheService.close", "line": 70, "method": "close", "native": false}, {"column": 22, "file": "/usr/src/app/src/server.js", "function": null, "line": 305, "method": null, "native": false}, {"column": 28, "file": "node:events", "function": "process.emit", "line": 519, "method": "emit", "native": false}]}