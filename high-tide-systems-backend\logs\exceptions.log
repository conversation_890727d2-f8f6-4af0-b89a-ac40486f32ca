{"timestamp": "2025-06-18 15:53:39", "level": "error", "message": "uncaughtException: write after end\nError: write after end\n    at writeAfterEnd (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:264:12)\n    at Writable.write (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:300:21)\n    at File.log (/usr/src/app/node_modules/winston/lib/winston/transports/file.js:222:34)\n    at RejectionStream._write (/usr/src/app/node_modules/winston/lib/winston/rejection-stream.js:46:29)\n    at doWrite (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:390:139)\n    at writeOrBuffer (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:381:5)\n    at Writable.write (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:302:11)\n    at DerivedLogger.ondata (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_readable.js:629:20)\n    at DerivedLogger.emit (node:events:531:35)\n    at addChunk (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_readable.js:279:12)", "error": {}, "stack": "Error: write after end\n    at writeAfterEnd (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:264:12)\n    at Writable.write (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:300:21)\n    at File.log (/usr/src/app/node_modules/winston/lib/winston/transports/file.js:222:34)\n    at RejectionStream._write (/usr/src/app/node_modules/winston/lib/winston/rejection-stream.js:46:29)\n    at doWrite (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:390:139)\n    at writeOrBuffer (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:381:5)\n    at Writable.write (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:302:11)\n    at DerivedLogger.ondata (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_readable.js:629:20)\n    at DerivedLogger.emit (node:events:531:35)\n    at addChunk (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_readable.js:279:12)", "exception": true, "date": "Wed Jun 18 2025 15:53:39 GMT+0000 (Coordinated Universal Time)", "process": {"pid": 138, "uid": 0, "gid": 0, "cwd": "/usr/src/app", "execPath": "/usr/local/bin/node", "version": "v20.17.0", "argv": ["/usr/local/bin/node", "/usr/src/app/src/server.js", "src/server.js"], "memoryUsage": {"rss": 139341824, "heapTotal": 45232128, "heapUsed": 43514688, "external": 3406239, "arrayBuffers": 132126}}, "os": {"loadavg": [1.92, 1.6, 1.62], "uptime": 4462.72}, "trace": [{"column": 12, "file": "/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js", "function": "writeAfterEnd", "line": 264, "method": null, "native": false}, {"column": 21, "file": "/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js", "function": "Writable.write", "line": 300, "method": "write", "native": false}, {"column": 34, "file": "/usr/src/app/node_modules/winston/lib/winston/transports/file.js", "function": "File.log", "line": 222, "method": "log", "native": false}, {"column": 29, "file": "/usr/src/app/node_modules/winston/lib/winston/rejection-stream.js", "function": "RejectionStream._write", "line": 46, "method": "_write", "native": false}, {"column": 139, "file": "/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js", "function": "doWrite", "line": 390, "method": null, "native": false}, {"column": 5, "file": "/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js", "function": "writeOr<PERSON>uffer", "line": 381, "method": null, "native": false}, {"column": 11, "file": "/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js", "function": "Writable.write", "line": 302, "method": "write", "native": false}, {"column": 20, "file": "/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_readable.js", "function": "DerivedLogger.ondata", "line": 629, "method": "ondata", "native": false}, {"column": 35, "file": "node:events", "function": "DerivedLogger.emit", "line": 531, "method": "emit", "native": false}, {"column": 12, "file": "/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_readable.js", "function": "addChunk", "line": 279, "method": null, "native": false}]}