"use client";

import React, { useState, useEffect } from "react";
import { useToast } from "@/contexts/ToastContext";
import { useAuth } from "@/contexts/AuthContext";
import { format } from "date-fns";
// import { utcToLocal, localToUTC } from "@/utils/dateFormatters";
import { Calendar } from "lucide-react";
import ModuleModal from "@/components/ui/ModuleModal";
import { appointmentService } from "@/app/modules/scheduler/services/appointmentService";
import insuranceLimitService from "@/app/modules/scheduler/services/insuranceLimitService";
import insuranceServiceLimitService from "@/app/modules/scheduler/services/insuranceServiceLimitService";
import insurancesService from "@/app/modules/people/services/insurancesService";
import ConfirmationDialog from "@/components/ui/ConfirmationDialog";

// Importando os componentes refatorados
import BasicInfoForm from "./appointmentModal/BasicInfoForm";
import SpecialtySelector from "./appointmentModal/SpecialtySelector";
import AppointmentSelectors from "./appointmentModal/AppointmentSelectors";
import InsuranceLimitsDisplay from "./InsuranceLimitsDisplay";
import AppointmentDates from "./appointmentModal/AppointmentDates";
import AppointmentStatus from "./appointmentModal/AppointmentStatus";
import SequentialAppointments from "./appointmentModal/SequentialAppointments";
import RecurrenceSettings from "./appointmentModal/RecurrenceSettings";
import ModuleModalFooter from "./appointmentModal/ModuleModalFooter";

export const AppointmentModal = ({
  isOpen,
  onClose,
  selectedDate,
  selectedAppointment,
  onAppointmentChange,
  checkAvailability,
  canCreate = false,
  canEdit = false,
  canDelete = false
}) => {
  // Função para forçar o fechamento do modal
  const forceCloseModal = () => {
    // Resetar TODOS os estados que podem estar impedindo o fechamento
    setConfirmationDialogOpen(false);
    // Removido setCanCloseMainModal pois não existe mais
    setIsLoading(false);

    // Chamar onClose diretamente
    onClose();
  };
  const { toast_success, toast_error } = useToast();
  const [formData, setFormData] = useState({
    title: "",
    description: "",
    specialty: "",
    providerId: "",
    personId: "",
    locationId: "",
    serviceTypeId: "",
    insuranceId: "",
    startDate: null,
    endDate: null,
    sequentialAppointments: 1,
    status: "PENDING",
    recurrence: {
      enabled: false,
      type: "OCCURRENCES",
      numberOfOccurrences: 1,
      endDate: null,
      patterns: [],
    },
  });

  const [providers, setProviders] = useState([]);
  const [filteredProviders, setFilteredProviders] = useState([]);
  const [specialties, setSpecialties] = useState([]);
  const [persons, setPersons] = useState([]);
  const [locations, setLocations] = useState([]);
  const [serviceTypes, setServiceTypes] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [personInsurances, setPersonInsurances] = useState([]);
  const [isLoadingInsurances, setIsLoadingInsurances] = useState(false);
  const [isCheckingLimits, setIsCheckingLimits] = useState(false);
  const [limitInfo, setLimitInfo] = useState(null);
  const [confirmationDialogOpen, setConfirmationDialogOpen] = useState(false);
  const [actionToConfirm, setActionToConfirm] = useState(null);
  // Removido canCloseMainModal pois não é mais necessário

  // Efeito para pré-carregar dados quando o modal é aberto com um agendamento existente
  useEffect(() => {
    if (isOpen && selectedAppointment) {
      console.log("Modal aberto com agendamento existente, pré-carregando dados");
      console.log("Dados completos do agendamento selecionado:", selectedAppointment);

      // Verificar se o ID está definido
      if (!selectedAppointment.id || selectedAppointment.id === "undefined") {
        console.error("ERRO: ID do agendamento inválido!", selectedAppointment.id);
        return;
      }

      // Verificar se temos o personId
      if (!selectedAppointment.personId) {
        console.warn("ALERTA: personId não encontrado no agendamento selecionado!");
        // Não temos personId, mas não vamos buscar da API novamente
        // Apenas exibir um aviso e continuar com os dados que temos
        console.log("Continuando com os dados disponíveis sem personId");
      }

      // Carregar convênios para o paciente se tivermos personId
      if (selectedAppointment.personId) {
        loadPersonInsurances(selectedAppointment.personId, selectedAppointment.insuranceId);

        // Carregar tipos de serviço se tivermos convênio
        if (selectedAppointment.insuranceId) {
          loadServiceTypes(selectedAppointment.personId, selectedAppointment.insuranceId, selectedAppointment.serviceTypeId);
        }
      }
    }
  }, [isOpen, selectedAppointment]);

  // Função para carregar convênios do paciente
  const loadPersonInsurances = (personId, insuranceId) => {
    if (!personId) return;

    console.log(`Carregando convênios para o paciente ID: ${personId}`);
    setIsLoadingInsurances(true);

    insurancesService.listPersonInsurances(personId)
      .then(insurances => {
        console.log(`Pré-carregados ${insurances?.length || 0} convênios para o paciente ${personId}`);
        setPersonInsurances(insurances || []);

        // Verificar se o convênio do agendamento está na lista
        if (insuranceId && insurances && insurances.length > 0) {
          const insuranceExists = insurances.some(ins =>
            (ins.insuranceId || ins.id) === insuranceId
          );

          if (!insuranceExists) {
            console.log(`Convênio ${insuranceId} não encontrado na lista, adicionando manualmente`);
            // Adicionar o convênio manualmente à lista
            if (selectedAppointment.insurance) {
              const manualInsurance = {
                id: insuranceId,
                insuranceId: insuranceId,
                name: selectedAppointment.insurance.name || "Convênio",
                insurance: selectedAppointment.insurance
              };
              setPersonInsurances(prev => [...prev, manualInsurance]);
            } else {
              // Tentar buscar o convênio do backend
              insurancesService.getInsuranceById(insuranceId)
                .then(insuranceData => {
                  if (insuranceData) {
                    const manualInsurance = {
                      id: insuranceId,
                      insuranceId: insuranceId,
                      name: insuranceData.name || "Convênio",
                      insurance: insuranceData
                    };
                    setPersonInsurances(prev => [...prev, manualInsurance]);
                  }
                })
                .catch(error => {
                  console.error("Erro ao buscar convênio do backend:", error);
                });
            }
          }
        }
      })
      .catch(error => {
        console.error("Erro ao pré-carregar convênios:", error);
      })
      .finally(() => {
        setIsLoadingInsurances(false);
      });
  };

  // Função para carregar tipos de serviço
  const loadServiceTypes = (personId, insuranceId, serviceTypeId) => {
    if (!personId || !insuranceId) return;

    console.log(`Carregando tipos de serviço para o paciente ID: ${personId} e convênio ID: ${insuranceId}`);
    setIsLoading(true);

    insuranceServiceLimitService.getServiceTypesByInsurance(insuranceId, personId)
      .then(serviceTypes => {
        console.log(`Pré-carregados ${serviceTypes?.length || 0} tipos de serviço`);
        setServiceTypes(serviceTypes || []);

        // Verificar se o tipo de serviço do agendamento está na lista
        if (serviceTypeId && serviceTypes && serviceTypes.length > 0) {
          const serviceTypeExists = serviceTypes.some(type => type.id === serviceTypeId);

          if (!serviceTypeExists) {
            console.log(`Tipo de serviço ${serviceTypeId} não encontrado na lista, adicionando manualmente`);
            // Adicionar o tipo de serviço manualmente à lista
            if (selectedAppointment.serviceType) {
              const manualServiceType = {
                id: serviceTypeId,
                name: selectedAppointment.serviceType.name || "Tipo de Serviço",
                value: selectedAppointment.serviceType.value || "0"
              };
              setServiceTypes(prev => [...prev, manualServiceType]);
            } else {
              // Tentar buscar o tipo de serviço do backend
              insuranceServiceLimitService.getServiceTypeById(serviceTypeId)
                .then(serviceTypeData => {
                  if (serviceTypeData) {
                    const manualServiceType = {
                      id: serviceTypeId,
                      name: serviceTypeData.name || "Tipo de Serviço",
                      value: serviceTypeData.value || "0"
                    };
                    setServiceTypes(prev => [...prev, manualServiceType]);
                  }
                })
                .catch(error => {
                  console.error("Erro ao buscar tipo de serviço do backend:", error);
                });
            }
          }
        }
      })
      .catch(error => {
        console.error("Erro ao pré-carregar tipos de serviço:", error);
      })
      .finally(() => {
        setIsLoading(false);
      });
  };

  // Garantir que o modal possa ser fechado quando isOpen mudar para false
  useEffect(() => {
    if (!isOpen) {
      // Resetar TODOS os estados que podem estar impedindo o fechamento
      setConfirmationDialogOpen(false);
      setIsLoading(false);

      // Limpar formulário
      setFormData({
        title: "",
        description: "",
        specialty: "",
        providerId: "",
        personId: "",
        locationId: "",
        serviceTypeId: "",
        insuranceId: "",
        startDate: null,
        endDate: null,
        sequentialAppointments: 1,
        status: "PENDING",
        recurrence: {
          enabled: false,
          type: "OCCURRENCES",
          numberOfOccurrences: 1,
          endDate: null,
          patterns: [],
        },
      });
    }
  }, [isOpen]);

  // Verificar se o modal deve exibir modo de edição ou criação
  const isEditMode = !!selectedAppointment;

  // Verificar se o usuário tem permissão para a ação atual
  const hasPermission = isEditMode ? canEdit : canCreate;

  // Verificar se o usuário é um cliente
  const { user } = useAuth();
  const isClient = user?.isClient;

  // Modo somente leitura para clientes
  const isReadOnlyMode = isClient && isEditMode;

  // Se não tiver permissão e não for cliente em modo somente leitura, exibir mensagem
  useEffect(() => {
    if (isOpen && !hasPermission && !isReadOnlyMode) {
      toast_error(isEditMode
        ? "Você não tem permissão para editar agendamentos."
        : "Você não tem permissão para criar agendamentos."
      );
    }
  }, [isOpen, hasPermission, isEditMode, isReadOnlyMode, toast_error]);

  // Função para extrair especialidades únicas dos provedores
  const extractSpecialties = (providersList) => {
    const uniqueSpecialties = [...new Set(
      providersList
        .filter(provider => provider && (provider.profession || provider.professionObj?.name))
        .map(provider => provider.profession || provider.professionObj?.name)
    )].sort();

    return uniqueSpecialties;
  };

  // Filtrar provedores baseado na especialidade selecionada
  useEffect(() => {
    if (formData.specialty) {
      const filtered = providers.filter(
        provider => {
          const providerProfession = provider.profession || provider.professionObj?.name;
          return providerProfession === formData.specialty;
        }
      );
      setFilteredProviders(filtered);

      // Se o provedor atualmente selecionado não estiver nesta especialidade, limpe a seleção
      if (formData.providerId && !filtered.some(p => p.id === formData.providerId)) {
        setFormData(prev => ({
          ...prev,
          providerId: ""
        }));
      }
    } else {
      setFilteredProviders(providers);
    }
  }, [formData.specialty, providers, formData.providerId]);

  // Carregar dados para os selects
  useEffect(() => {
    const loadData = async () => {
      setIsLoading(true);
      try {
        console.log("Loading appointment modal data...");

        // Load data separately to better debug any issues
        console.log("Loading providers...");
        const providersRes = await appointmentService.getProviders();
        console.log(`Loaded ${providersRes?.length || 0} providers`);

        console.log("Loading persons...");
        const personsRes = await appointmentService.getPersons();
        console.log(`Loaded ${personsRes?.length || 0} persons`);

        console.log("Loading locations...");
        const locationsRes = await appointmentService.getLocations();
        console.log(`Loaded ${locationsRes?.length || 0} locations`);

        // Set state with valid data only
        const validProviders = providersRes || [];
        setProviders(validProviders);
        setFilteredProviders(validProviders);
        setPersons(personsRes || []);

        // Extract and set unique specialties
        const extractedSpecialties = extractSpecialties(validProviders);
        setSpecialties(extractedSpecialties);
        setLocations(locationsRes || []);

        // Log what was set
        console.log("Set providers:", validProviders?.length || 0);
        console.log("Set specialties:", extractedSpecialties?.length || 0);
        console.log("Set persons:", personsRes?.length || 0);
        console.log("Set locations:", locationsRes?.length || 0);
      } catch (error) {
        console.error("Erro ao carregar dados:", error);
        toast_error("Erro ao carregar dados necessários. Por favor, tente novamente.");
      } finally {
        setIsLoading(false);
      }
    };

    if (isOpen) {
      loadData();
    }
  }, [isOpen]);

  // Carregar os convênios do paciente selecionado
  useEffect(() => {
    const loadPersonInsurances = async () => {
      // Se não tiver um personId (paciente) selecionado, não fazer nada
      if (!formData.personId) {
        setPersonInsurances([]);
        return;
      }

      setIsLoadingInsurances(true);
      try {
        console.log(`Carregando convênios do paciente ID: ${formData.personId}`);

        const insurancesRes = await insurancesService.listPersonInsurances(formData.personId);

        if (insurancesRes && insurancesRes.length > 0) {
          console.log(`Carregados ${insurancesRes.length} convênios do paciente`);
          console.log("Primeiro convênio:", insurancesRes[0]);
          setPersonInsurances(insurancesRes);

          // Se estamos editando um agendamento existente e o convênio não está na lista,
          // não limpar o convênio selecionado
          if (formData.id && formData.insuranceId) {
            const insuranceExists = insurancesRes.some(ins =>
              (ins.insuranceId || ins.id) === formData.insuranceId
            );

            if (!insuranceExists) {
              console.log(`Convênio ${formData.insuranceId} não encontrado na lista, mas mantendo-o selecionado para edição`);
            }
          }
          // Removido o reset do convênio para novos agendamentos
        } else {
          console.warn("Nenhum convênio encontrado para este paciente");

          // Manter o convênio selecionado mesmo se não houver convênios disponíveis
          // Isso permite que o usuário selecione "Sem convênio" manualmente
        }
      } catch (error) {
        console.error(`Erro ao carregar convênios do paciente ${formData.personId}:`, error);
      } finally {
        setIsLoadingInsurances(false);
      }
    };

    // Carregar os convênios sempre que o paciente mudar
    loadPersonInsurances();
  }, [formData.personId, formData.id, formData.insuranceId]);

  // Preencher dados do formulário baseado no evento selecionado ou data
  useEffect(() => {
    if (selectedAppointment) {
      console.log("Selected appointment:", selectedAppointment);

      // Verificar se o ID está definido
      if (!selectedAppointment.id) {
        console.error("ERRO: ID do agendamento não definido!");
        return;
      }

      // Buscar especialidade do provedor selecionado
      const provider = providers.find(p => p.id === selectedAppointment.providerId);
      const providerSpecialty = provider?.profession || provider?.professionObj?.name || "";

      // Edição de agendamento existente
      // Usar setTimeout para garantir que este efeito seja executado após os outros efeitos
      // que podem estar tentando limpar os dados
      setTimeout(() => {
        console.log("Preenchendo formulário com dados do agendamento selecionado");
        console.log("Dados do agendamento:", {
          id: selectedAppointment.id,
          personId: selectedAppointment.personId,
          insuranceId: selectedAppointment.insuranceId,
          serviceTypeId: selectedAppointment.serviceTypeId
        });

        // Garantir que temos valores válidos para todos os campos
        const formDataValues = {
          id: selectedAppointment.id,
          title: selectedAppointment.title || "",
          description: selectedAppointment.description || "",
          specialty: providerSpecialty || "",
          providerId: selectedAppointment.providerId || "",
          personId: selectedAppointment.personId || "",
          locationId: selectedAppointment.locationId || "",
          serviceTypeId: selectedAppointment.serviceTypeId || "",
          insuranceId: selectedAppointment.insuranceId || "",
          startDate: selectedAppointment.startDate || new Date().toISOString(),
          endDate: selectedAppointment.endDate || new Date(Date.now() + 3600000).toISOString(),
          sequentialAppointments: 1,
          status: selectedAppointment.status || "PENDING",
          recurrence: {
            enabled: false,
            type: "OCCURRENCES",
            numberOfOccurrences: 1,
            endDate: null,
            patterns: [],
          },
        };

        console.log("Valores finais do formulário:", formDataValues);
        setFormData(formDataValues);
      }, 100); // Pequeno delay para garantir a ordem correta de execução
    } else if (selectedDate) {
      // Novo agendamento
      // Usar os objetos Date diretamente, mantendo o horário local que o usuário clicou
      console.log(`[MODAL-DEBUG] selectedDate recebido:`, {
        start: selectedDate.start?.toLocaleString(),
        end: selectedDate.end?.toLocaleString(),
        temEnd: !!selectedDate.end,
        tipo: typeof selectedDate
      });

      // Para a data de início, usar o objeto Date do calendário diretamente
      const startDate = selectedDate.start;

      // Para a data de término, SEMPRE usar duração de 1 hora, ignorando o valor de end do calendário
      console.log(`[MODAL-DEBUG] FORÇANDO endDate com duração de 60 minutos, ignorando o valor de end`);
      const endDate = new Date(selectedDate.start.getTime() + 60 * 60 * 1000);
      console.log(`[MODAL-DEBUG] Duração forçada (minutos): ${(endDate - startDate) / (60 * 1000)}`);

      console.log(`[MODAL-INIT] Datas do calendário (horário local) - Start: ${startDate.toLocaleString()}, End: ${endDate.toLocaleString()}`);

      // Converter para strings ISO apenas para armazenamento no formData
      // Importante: toISOString() converte para UTC, mas vamos usar isso apenas para armazenamento interno
      const startISOString = startDate.toISOString();
      const endISOString = endDate.toISOString();

      setFormData({
        title: "",
        description: "",
        specialty: "",
        providerId: "",
        personId: "",
        locationId: "",
        serviceTypeId: "",
        insuranceId: "",
        startDate: startISOString,
        endDate: endISOString,
        sequentialAppointments: 1,
        status: "PENDING",
        recurrence: {
          enabled: false,
          type: "OCCURRENCES",
          numberOfOccurrences: 1,
          endDate: null,
          patterns: [],
        },
      });
    }
  }, [selectedAppointment, selectedDate, providers]);

  // Carregar tipos de serviço com base no convênio selecionado ou todos os tipos se não houver convênio
  useEffect(() => {
    const loadServiceTypes = async () => {
      // Se não tiver paciente, limpar tipos de serviço e retornar
      if (!formData.personId) {
        setServiceTypes([]);

        // Se não estamos editando um agendamento existente, resetar apenas o tipo de serviço
        if (!formData.id && formData.serviceTypeId) {
          setFormData(prev => ({
            ...prev,
            serviceTypeId: ""
          }));
        }
        return;
      }

      setIsLoading(true);
      try {
        let serviceTypesData = [];

        if (formData.insuranceId) {
          // Se há convênio, buscar tipos de serviço com limites configurados
          console.log(`Carregando tipos de serviço para o paciente ID: ${formData.personId} e convênio ID: ${formData.insuranceId}`);

          serviceTypesData = await insuranceServiceLimitService.getServiceTypesByInsurance(
            formData.insuranceId,
            formData.personId
          );

          console.log(`Encontrados ${serviceTypesData?.length || 0} tipos de serviço com limites configurados`);
        } else {
          // Se não há convênio (agendamento particular), buscar todos os tipos de serviço da empresa
          console.log(`Carregando todos os tipos de serviço para agendamento particular`);

          // Importar o serviço de tipos de serviço
          const { serviceTypeService } = await import('@/app/modules/scheduler/services/serviceTypeService');
          const response = await serviceTypeService.getServiceTypes();
          serviceTypesData = response.serviceTypes || response.data || [];

          console.log(`Encontrados ${serviceTypesData?.length || 0} tipos de serviço disponíveis`);
        }

        if (serviceTypesData && serviceTypesData.length > 0) {
          setServiceTypes(serviceTypesData);

          // Se estamos editando um agendamento existente e o tipo de serviço não está na lista,
          // adicionar o tipo de serviço atual à lista
          if (formData.id && formData.serviceTypeId) {
            const serviceTypeExists = serviceTypesData.some(type => type.id === formData.serviceTypeId);

            if (!serviceTypeExists && selectedAppointment?.serviceType) {
              console.log(`Tipo de serviço ${formData.serviceTypeId} não encontrado na lista, adicionando à lista`);
              const currentServiceType = {
                id: formData.serviceTypeId,
                name: selectedAppointment.serviceType.name || "Tipo de Serviço",
                value: selectedAppointment.serviceType.value || "0"
              };
              setServiceTypes(prev => [...prev, currentServiceType]);
            }
          }

        } else {
          console.warn("Nenhum tipo de serviço encontrado");
          setServiceTypes([]);

          // Se não estamos editando e temos um tipo de serviço selecionado, limpar apenas o tipo de serviço
          if (!formData.id && formData.serviceTypeId) {
            setFormData(prev => ({
              ...prev,
              serviceTypeId: ""
            }));
          }

          // Exibir mensagem apenas se houver convênio mas não houver tipos de serviço
          if (formData.insuranceId) {
            toast_error(
              "Não há tipos de serviço configurados para este paciente e convênio. " +
              "Por favor, configure os limites de serviço para esta combinação de paciente e convênio."
            );
          }
        }
      } catch (error) {
        console.error("Erro ao carregar tipos de serviço:", error);
        setServiceTypes([]);
        toast_error("Erro ao carregar tipos de serviço. Por favor, tente novamente.");
      } finally {
        setIsLoading(false);
      }
    };

    loadServiceTypes();
  }, [formData.insuranceId, formData.personId, formData.id, formData.serviceTypeId, selectedAppointment, toast_error]);

  // Verificar limites de convênio
  useEffect(() => {
    const checkInsuranceLimits = async () => {
      // Só verifica se tiver pessoa, convênio e tipo de serviço selecionados
      if (formData.personId && formData.insuranceId && formData.serviceTypeId && formData.startDate) {
        setIsCheckingLimits(true);
        try {
          console.log(`Verificando limites para data do agendamento: ${formData.startDate}`);

          const result = await insuranceLimitService.checkLimitAvailability({
            personId: formData.personId,
            insuranceId: formData.insuranceId,
            serviceTypeId: formData.serviceTypeId,
            appointmentDate: new Date(formData.startDate) // Passar a data do agendamento
          });

          console.log(`Resultado da verificação de limites:`, result);
          setLimitInfo(result.usage);

          // Se limite foi atingido, mostrar erro apenas para novos agendamentos
          // Para edição, apenas mostrar um aviso informativo
          if (!result.available) {
            if (!isEditMode) {
              toast_error(result.message);
            } else {
              // Para edição, mostrar um aviso mais suave
              console.log("Limite atingido, mas permitindo edição do agendamento existente");
            }
          }
        } catch (error) {
          console.error("Erro ao verificar limites:", error);
        } finally {
          setIsCheckingLimits(false);
        }
      } else {
        // Limpar dados de limite se algum campo necessário não estiver preenchido
        setLimitInfo(null);
      }
    };

    checkInsuranceLimits();
  }, [formData.personId, formData.insuranceId, formData.serviceTypeId, formData.startDate, toast_error, isEditMode]);

  // Validar agendamento antes de enviar
  const validateAppointment = async (data = formData) => {
    // Validação básica
    if (!data.title) {
      toast_error("Por favor, informe um título para o agendamento.");
      return false;
    }

    if (!data.providerId) {
      toast_error("Por favor, selecione um profissional.");
      return false;
    }

    if (!data.personId) {
      toast_error("Por favor, selecione um paciente.");
      return false;
    }

    if (!data.locationId) {
      toast_error("Por favor, selecione um local.");
      return false;
    }

    if (!data.serviceTypeId) {
      toast_error("Por favor, selecione um tipo de serviço.");
      return false;
    }

    // Verificar limites de convênio apenas para novos agendamentos
    // Para edição, permitir mesmo que o limite tenha sido atingido
    if (!isEditMode && limitInfo && limitInfo.monthly && !limitInfo.monthly.unlimited) {
      const isLimitReached = limitInfo.monthly.used >= limitInfo.monthly.limit;
      if (isLimitReached) {
        console.log("Limite de convênio atingido, bloqueando criação de novo agendamento");
        toast_error(`Limite mensal de ${limitInfo.monthly.limit} agendamentos atingido. Você já utilizou ${limitInfo.monthly.used} agendamentos.`);
        return false;
      }
    }

    // Verificar se há conflitos com agendamentos existentes
    try {
      // Verificar disponibilidade tanto para novos agendamentos quanto para edição
      // Verificar se o horário está disponível para o profissional no servidor
      const providerCheckResponse = await appointmentService.checkAvailability({
        providerId: data.providerId,
        startDate: data.startDate,
        endDate: data.endDate,
        excludeId: data.id // Para edição, excluir o próprio agendamento
      });

      if (!providerCheckResponse.available) {
        if (providerCheckResponse.conflict) {
          toast_error("Horário indisponível para o profissional selecionado.");
        } else {
          toast_error("Horário indisponível para o profissional.");
        }
        return false;
      }

      // Verificar se o paciente já tem um agendamento no mesmo horário
      const patientCheckResponse = await appointmentService.checkPatientAvailability({
        personId: data.personId,
        startDate: data.startDate,
        endDate: data.endDate,
        excludeId: data.id // Para edição, excluir o próprio agendamento
      });

      if (!patientCheckResponse.available) {
        if (patientCheckResponse.conflict) {
          toast_error("Paciente já possui um agendamento neste horário.");
        } else {
          toast_error("Horário indisponível para o paciente.");
        }
        return false;
      }
    } catch (error) {
      console.error("Erro ao verificar disponibilidade no servidor:", error);
      // Não bloquear o fluxo em caso de erro na verificação
    }

    // Validar recorrência, se estiver habilitada (apenas para novos agendamentos)
    if (!isEditMode && data.recurrence.enabled) {
      // Validar padrões de recorrência
      if (!data.recurrence.patterns || data.recurrence.patterns.length === 0) {
        toast_error("Por favor, selecione pelo menos um dia da semana para a recorrência.");
        return false;
      }

      // Validar data final ou número de ocorrências
      if (data.recurrence.type === 'END_DATE') {
        if (!data.recurrence.endDate) {
          toast_error("Por favor, informe uma data final para a recorrência.");
          return false;
        }

        const endDate = new Date(data.recurrence.endDate);
        const tomorrow = new Date();
        tomorrow.setDate(tomorrow.getDate() + 1);

        if (endDate <= tomorrow) {
          toast_error("A data final da recorrência deve ser pelo menos 1 dia após a data atual.");
          return false;
        }
      } else if (data.recurrence.type === 'OCCURRENCES') {
        if (!data.recurrence.numberOfOccurrences || data.recurrence.numberOfOccurrences < 1) {
          toast_error("Por favor, informe um número válido de ocorrências para a recorrência.");
          return false;
        }
      }
    }

    // Verificar disponibilidade do profissional, se implementado
    if (checkAvailability) {
      const isAvailable = await checkAvailability(
        data.providerId,
        {
          start: data.startDate,
          end: data.endDate
        }
      );

      if (!isAvailable) {
        toast_error("O profissional selecionado não está disponível neste horário.");
        return false;
      }

      // Se estiver criando agendamentos sequenciais (apenas para novos agendamentos), verificar cada um deles
      if (!isEditMode && data.sequentialAppointments > 1) {
        // Converter strings ISO para objetos Date para cálculos
        const startDate = new Date(data.startDate);
        const endDate = new Date(data.endDate);
        let currentEndDate = endDate;

        // Duração fixa de 1 hora (3600000 ms) para agendamentos sequenciais
        const sequentialDuration = 3600000; // 1 hora em milissegundos

        console.log(`[SEQUENTIAL] Verificando ${data.sequentialAppointments} agendamentos sequenciais`);
        console.log(`[SEQUENTIAL] Agendamento inicial: ${startDate.toLocaleString()} - ${endDate.toLocaleString()}`);

        for (let i = 1; i < data.sequentialAppointments; i++) {
          // Criar o próximo agendamento sequencial
          // Começa exatamente quando o anterior termina
          const nextStartDate = new Date(currentEndDate.getTime());
          // Duração fixa de 1 hora
          const nextEndDate = new Date(nextStartDate.getTime() + sequentialDuration);

          console.log(`[SEQUENTIAL] Verificando agendamento #${i+1}: ${nextStartDate.toLocaleString()} - ${nextEndDate.toLocaleString()}`);

          // Converter para strings ISO para verificar disponibilidade
          const nextStartISO = nextStartDate.toISOString();
          const nextEndISO = nextEndDate.toISOString();

          // Verificar disponibilidade do profissional no servidor para este horário sequencial
          try {
            const providerServerCheckResponse = await appointmentService.checkAvailability({
              providerId: data.providerId,
              startDate: nextStartISO,
              endDate: nextEndISO,
              excludeId: data.id // Para edição, excluir o próprio agendamento
            });

            // Se não estiver disponível no servidor, mostrar mensagem de erro
            if (!providerServerCheckResponse.available) {
              // Formatar as datas para exibição
              const formatTime = (date) => format(date, 'HH:mm');

              if (providerServerCheckResponse.conflict) {
                const conflict = providerServerCheckResponse.conflict;
                const conflictStart = new Date(conflict.startDate);
                const conflictEnd = new Date(conflict.endDate);

                const appointmentTitle = conflict.title || "Sem título";
                toast_error(`O ${i + 1}º agendamento sequencial (${formatTime(nextStartDate)} - ${formatTime(nextEndDate)}) conflita com o agendamento "${appointmentTitle}" (${format(conflictStart, 'HH:mm')} - ${format(conflictEnd, 'HH:mm')}).`);
              } else {
                toast_error(`O profissional não está disponível para o ${i + 1}º agendamento sequencial (${formatTime(nextStartDate)} - ${formatTime(nextEndDate)}).`);
              }
              return false;
            }

            // Verificar disponibilidade do paciente no servidor para este horário sequencial
            const patientServerCheckResponse = await appointmentService.checkPatientAvailability({
              personId: data.personId,
              startDate: nextStartISO,
              endDate: nextEndISO,
              excludeId: data.id // Para edição, excluir o próprio agendamento
            });

            // Se não estiver disponível no servidor, mostrar mensagem de erro
            if (!patientServerCheckResponse.available) {
              // Formatar as datas para exibição
              const formatTime = (date) => format(date, 'HH:mm');

              if (patientServerCheckResponse.conflict) {
                const conflict = patientServerCheckResponse.conflict;
                const conflictStart = new Date(conflict.startDate);
                const conflictEnd = new Date(conflict.endDate);

                const appointmentTitle = conflict.title || "Sem título";
                toast_error(`O paciente já possui um agendamento "${appointmentTitle}" (${format(conflictStart, 'HH:mm')} - ${format(conflictEnd, 'HH:mm')}) que conflita com o ${i + 1}º agendamento sequencial (${formatTime(nextStartDate)} - ${formatTime(nextEndDate)}).`);
              } else {
                toast_error(`O paciente já possui um agendamento no horário do ${i + 1}º agendamento sequencial (${formatTime(nextStartDate)} - ${formatTime(nextEndDate)}).`);
              }
              return false;
            }
          } catch (error) {
            console.error(`Erro ao verificar disponibilidade do ${i + 1}º agendamento sequencial:`, error);
            // Continuar com a verificação local em caso de erro na verificação do servidor
          }

          // Verificar disponibilidade local (horários de trabalho)
          const isNextSlotAvailable = await checkAvailability(
            data.providerId,
            {
              start: nextStartISO,
              end: nextEndISO
            }
          );

          if (!isNextSlotAvailable) {
            toast_error(`O profissional não está disponível para o ${i + 1}º agendamento sequencial (${format(nextStartDate, 'HH:mm')} - ${format(nextEndDate, 'HH:mm')}).`);
            return false;
          }

          currentEndDate = nextEndDate;
        }
      }
    }

    return true;
  };

  // Função para confirmar a ação antes de executar
  const confirmAction = async () => {
    if (!actionToConfirm) {
      return;
    }

    console.log('[CONFIRM-ACTION] Confirmando ação:', actionToConfirm.type);

    try {
      // Fechar o diálogo de confirmação imediatamente
      setConfirmationDialogOpen(false);

      // Garantir que o modal principal possa ser fechado

      if (actionToConfirm.type === "save") {
        await saveAppointment();
      } else if (actionToConfirm.type === "delete") {
        await deleteAppointment();
      }
    } catch (error) {
      console.error('[CONFIRM-ACTION] Erro ao executar ação:', error);
      // Garantir que o modal possa ser fechado mesmo em caso de erro
    } finally {
      // Garantir que o modal possa ser fechado após a ação, independente do resultado
      setTimeout(() => {
        console.log('[CONFIRM-ACTION] Garantindo que o modal possa ser fechado após a ação');
      }, 500);
    }
  };

  // Função para salvar o agendamento após confirmação
  const saveAppointment = async () => {
    setIsLoading(true);

    try {
      // As datas no formData estão em formato ISO string (UTC)
      // Isso é exatamente o que o backend espera, então não precisamos converter
      console.log(`[SAVE] Datas originais (UTC) - Start: ${formData.startDate}, End: ${formData.endDate}`);

      // Converter para objetos Date para exibir no log o horário local
      const startLocalDate = new Date(formData.startDate);
      const endLocalDate = new Date(formData.endDate);
      console.log(`[SAVE] Datas locais - Start: ${startLocalDate.toLocaleString()}, End: ${endLocalDate.toLocaleString()}`);

      // Criar objeto base com dados comuns
      const baseAppointmentData = {
        title: formData.title,
        description: formData.description,
        userId: formData.providerId,
        providerId: formData.providerId,
        personId: formData.personId,
        locationId: formData.locationId,
        serviceTypeId: formData.serviceTypeId,
        insuranceId: formData.insuranceId || null,
        // Enviar as datas em formato ISO (UTC) para o backend
        startDate: formData.startDate,
        endDate: formData.endDate,
        creatorId: formData.providerId,
        status: formData.status,
      };

      // Adicionar sequentialAppointments apenas para novos agendamentos
      const appointmentData = isEditMode
        ? baseAppointmentData
        : {
            ...baseAppointmentData,
            sequentialAppointments: formData.sequentialAppointments
          };

      console.log(`[SAVE] Enviando datas (UTC) - Start: ${appointmentData.startDate}, End: ${appointmentData.endDate}`);

      if (!isEditMode && formData.recurrence.enabled) {
        // Preparar dados de recorrência
        const recurrenceData = {
          ...appointmentData,
          recurrenceType: formData.recurrence.type,
          // Para o tipo OCCURRENCES, usar o número de ocorrências
          // Para o tipo END_DATE, usar a data final (já em formato ISO)
          recurrenceValue: formData.recurrence.type === "OCCURRENCES"
            ? formData.recurrence.numberOfOccurrences
            : formData.recurrence.endDate,
          // Mapear os padrões de recorrência
          patterns: formData.recurrence.patterns.map((pattern) => {
            console.log(`[RECURRENCE] Padrão: dia ${pattern.dayOfWeek}, ${pattern.startTime} - ${pattern.endTime}`);
            return {
              dayOfWeek: pattern.dayOfWeek,
              startTime: pattern.startTime,
              endTime: pattern.endTime,
            };
          }),
        };

        console.log(`[RECURRENCE] Criando recorrência do tipo ${recurrenceData.recurrenceType}`);
        console.log(`[RECURRENCE] Valor: ${recurrenceData.recurrenceValue}`);
        console.log(`[RECURRENCE] Padrões: ${recurrenceData.patterns.length}`);

        await appointmentService.createRecurrence(recurrenceData);
      } else {
        if (formData.id) {
          // Verificar se o ID é válido antes de atualizar
          if (!formData.id || formData.id === "undefined") {
            console.error("[SAVE] ID do agendamento inválido para atualização:", formData.id);
            throw new Error("ID do agendamento inválido. Não é possível atualizar.");
          }

          console.log("[SAVE] Atualizando agendamento com ID:", formData.id);

          try {
            // Update existente
            await appointmentService.updateAppointment(
              formData.id,
              {
                ...appointmentData,
                updatedBy: formData.providerId
              }
            );
          } catch (updateError) {
            console.error("[SAVE] Erro ao atualizar agendamento:", updateError);

            // Verificar se o erro é 404 (agendamento não encontrado)
            if (updateError.response && updateError.response.status === 404) {
              console.log("[SAVE] Agendamento não encontrado, tentando criar um novo");

              // Tentar criar um novo agendamento com os mesmos dados
              await appointmentService.createAppointment(appointmentData);
            } else {
              // Se não for 404, propagar o erro
              throw updateError;
            }
          }
        } else {
          // Novo agendamento
          await appointmentService.createAppointment(appointmentData);
        }
      }

      // Exibir mensagem de sucesso
      let successMessage = "";

      if (isEditMode) {
        successMessage = "Agendamento atualizado com sucesso!";
      } else if (formData.recurrence.enabled) {
        successMessage = "Recorrência criada com sucesso!";
      } else {
        successMessage = "Agendamento criado com sucesso!";
      }

      toast_success({
        title: "Sucesso",
        message: successMessage
      });

      onAppointmentChange();
      onClose(); // Fechar o modal de agendamento apenas após sucesso
    } catch (error) {
      console.error("Erro ao salvar agendamento:", error);

      // Extrair mensagem de erro da resposta da API, se disponível
      let errorMsg = "Erro ao salvar o agendamento. Por favor, tente novamente.";

      if (error.response && error.response.data) {
        if (error.response.data.message) {
          errorMsg = error.response.data.message;

          // Adicionar informações mais detalhadas para conflitos de horário
          if (error.response.data.reason === "CONFLICT" && error.response.data.conflictData) {
            const conflict = error.response.data.conflictData;
            const conflictStart = new Date(conflict.startDate);
            const conflictEnd = new Date(conflict.endDate);

            // Formatar as datas para exibição
            const formatDate = (date) => {
              try {
                if (!date) return "horário não especificado";
                // Verificar se a data é válida
                if (isNaN(date.getTime())) return "horário não disponível";
                return date.toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'});
              } catch (error) {
                console.error("Erro ao formatar data:", error, date);
                return "horário não disponível";
              }
            };

            const appointmentTitle = conflict.title || "Sem título";
            errorMsg += ` Existe um agendamento conflitante "${appointmentTitle}" no período ${formatDate(conflictStart)} - ${formatDate(conflictEnd)}.`;
          }
        } else if (error.response.data.error) {
          errorMsg = error.response.data.error;
        }
      } else if (error.message) {
        errorMsg = error.message;
      }

      // Exibir toast de erro
      toast_error({
        title: "Erro ao salvar agendamento",
        message: errorMsg
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Função para excluir agendamento após confirmação
  const deleteAppointment = async () => {
    // Verificar se o ID é válido antes de excluir
    if (!formData.id || formData.id === "undefined") {
      console.error("[DELETE] ID do agendamento inválido para exclusão:", formData.id);
      toast_error("ID do agendamento inválido. Não é possível excluir.");
      return;
    }

    setIsLoading(true);

    console.log("[DELETE] Excluindo agendamento com ID:", formData.id);

    try {
      try {
        await appointmentService.deleteAppointment(formData.id);
      } catch (deleteError) {
        console.error("[DELETE] Erro ao excluir agendamento:", deleteError);

        // Verificar se o erro é 404 (agendamento não encontrado)
        if (deleteError.response && deleteError.response.status === 404) {
          // Se o agendamento não existe, considerar como excluído com sucesso
          console.log("[DELETE] Agendamento não encontrado, considerando como excluído");
          // Não propagar o erro, continuar como se tivesse excluído com sucesso
        } else {
          // Se não for 404, propagar o erro
          throw deleteError;
        }
      }

      // Exibir mensagem de sucesso
      toast_success({
        title: "Sucesso",
        message: "Agendamento excluído com sucesso!"
      });

      onAppointmentChange();
      onClose(); // Fechar o modal de agendamento apenas após sucesso
    } catch (error) {
      console.error("Erro ao excluir agendamento:", error);

      // Extrair mensagem de erro da resposta da API, se disponível
      let errorMsg = "Erro ao excluir o agendamento. Por favor, tente novamente.";

      if (error.response && error.response.data) {
        if (error.response.data.message) {
          errorMsg = error.response.data.message;
        } else if (error.response.data.error) {
          errorMsg = error.response.data.error;
        }
      } else if (error.message) {
        errorMsg = error.message;
      }

      // Exibir toast de erro
      toast_error({
        title: "Erro ao excluir agendamento",
        message: errorMsg
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    // Verificar novamente a permissão antes de enviar
    if (!hasPermission || isReadOnlyMode) {
      toast_error(isEditMode
        ? "Você não tem permissão para editar agendamentos."
        : "Você não tem permissão para criar agendamentos."
      );
      return;
    }

    // Validar dados do agendamento
    const isValid = await validateAppointment();
    if (!isValid) {
      return;
    }

    // Preparar mensagem de confirmação
    let actionMessage = "";
    let actionTitle = "";

    if (isEditMode) {
      // Mensagem para edição de agendamento existente
      actionTitle = "Atualizar agendamento";
      actionMessage = `Deseja atualizar o agendamento "${formData.title}"?`;
    } else if (formData.recurrence.enabled) {
      // Mensagem para criação de agendamento recorrente
      actionTitle = "Criar agendamentos recorrentes";
      actionMessage = `Deseja criar uma série de agendamentos recorrentes para ${formData.title}?`;
    } else {
      // Mensagem para criação de agendamento normal ou sequencial
      actionTitle = formData.sequentialAppointments > 1
        ? "Criar agendamentos sequenciais"
        : "Criar agendamento";
      actionMessage = formData.sequentialAppointments > 1
        ? `Deseja criar ${formData.sequentialAppointments} agendamentos sequenciais para "${formData.title}"?`
        : `Deseja criar o agendamento "${formData.title}"?`;
    }

    // Configurar ação para confirmação
    setActionToConfirm({
      type: "save",
      message: actionMessage,
      title: actionTitle,
      variant: "info"
    });

    // Abrir diálogo de confirmação
    setConfirmationDialogOpen(true);
  };

  // Função para solicitar confirmação de exclusão
  const handleDelete = () => {
    // Verificar permissão de exclusão
    if (!canDelete) {
      toast_error("Você não tem permissão para excluir agendamentos.");
      return;
    }

    if (!formData.id) return;

    // Configurar ação para confirmação
    setActionToConfirm({
      type: "delete",
      message: `Tem certeza que deseja excluir o agendamento "${formData.title}"?`,
      title: "Excluir agendamento",
      variant: "danger"
    });

    // Abrir diálogo de confirmação
    setConfirmationDialogOpen(true);
  };

  // Função para calcular o horário do último agendamento sequencial
  const calculateLastAppointmentTime = () => {
    if (formData.sequentialAppointments <= 1 || !formData.startDate || !formData.endDate) {
      return null;
    }

    // Converter strings ISO para objetos Date para cálculos
    const endDate = new Date(formData.endDate);

    // Duração fixa de 1 hora (3600000 ms) para agendamentos sequenciais
    const sequentialDuration = 3600000; // 1 hora em milissegundos

    // Começar com o horário de término do primeiro agendamento
    let currentEndTime = endDate;

    // Calcular o horário do último agendamento
    for (let i = 1; i < formData.sequentialAppointments; i++) {
      // O próximo agendamento começa quando o anterior termina
      const nextStartTime = new Date(currentEndTime.getTime());
      // Duração fixa de 1 hora
      const nextEndTime = new Date(nextStartTime.getTime() + sequentialDuration);

      // Atualizar para o próximo agendamento
      currentEndTime = nextEndTime;
    }

    // O último agendamento começa 1 hora antes do horário final calculado
    const lastStartTime = new Date(currentEndTime.getTime() - sequentialDuration);
    const lastEndTime = currentEndTime;

    console.log(`[SEQUENTIAL-CALC] Último agendamento: ${lastStartTime.toLocaleString()} - ${lastEndTime.toLocaleString()}`);

    // Retornar os objetos Date diretamente
    return {
      start: lastStartTime,
      end: lastEndTime
    };
  };

  // Calcular o último horário para os agendamentos sequenciais
  // Usar useEffect para recalcular sempre que formData mudar
  const [lastAppointmentTime, setLastAppointmentTime] = useState(null);

  useEffect(() => {
    setLastAppointmentTime(calculateLastAppointmentTime());
  }, [formData.sequentialAppointments, formData.startDate, formData.endDate]);


  return (
    <>
      {/* Diálogo de confirmação */}
      <ConfirmationDialog
        isOpen={confirmationDialogOpen}
        onClose={() => {
          console.log('[CONFIRMATION-DIALOG] Fechando diálogo de confirmação');
          // Fechar o diálogo de confirmação
          setConfirmationDialogOpen(false);
        }}
        onConfirm={confirmAction}
        title={actionToConfirm?.title || "Confirmar ação"}
        message={actionToConfirm?.message || ""}
        variant={actionToConfirm?.variant || "info"}
        confirmText={actionToConfirm?.type === "delete" ? "Excluir" : "Confirmar"}
      />

      <ModuleModal
        isOpen={isOpen}
        onClose={() => {
          // Sempre usar forceCloseModal para garantir o fechamento
          forceCloseModal();
        }}
        animateExit={false} // Desativar animação de saída para evitar problemas
        title={selectedAppointment ? "Editar Agendamento" : "Novo Agendamento"}
        icon={<Calendar size={22} />}
        moduleColor="scheduler"
        size="xl"
        preventClose={false} // Nunca impedir o fechamento do modal
        onInteractOutside={() => {
          console.log('[OUTSIDE-CLICK] Clique fora do modal');
          // Sempre permitir o fechamento do modal
          forceCloseModal();
        }}
        footer={
          <ModuleModalFooter
            isEditMode={isEditMode}
            canDelete={canDelete && !isReadOnlyMode}
            hasPermission={hasPermission && !isReadOnlyMode}
            formData={formData}
            isLoading={isLoading}
            limitInfo={limitInfo}
            handleDelete={handleDelete}
            onClose={() => {
              console.log('[FOOTER-CLOSE] Tentativa de fechar o modal via footer');
              forceCloseModal(); // Forçar fechamento independente do estado
            }}
          />
        }
      >

        <form id="appointment-form" onSubmit={handleSubmit} className="overflow-y-auto dark:bg-gray-800 flex flex-col justify-between p-5">
          {/* Removido o bloco de exibição de erros no modal, agora usando toast */}

          <div className="space-y-4">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <div className="space-y-5">
                {/* Informações Básicas */}
                <BasicInfoForm
                  formData={formData}
                  setFormData={setFormData}
                  readOnly={isReadOnlyMode}
                />

                {/* Especialidade */}
                <SpecialtySelector
                  formData={formData}
                  setFormData={setFormData}
                  specialties={specialties}
                  readOnly={isReadOnlyMode}
                />

                {/* Seleções (Profissionais, Pacientes, Local, Tipo de Serviço, Convênio) */}
                <AppointmentSelectors
                  formData={formData}
                  setFormData={setFormData}
                  filteredProviders={filteredProviders}
                  persons={persons}
                  locations={locations}
                  serviceTypes={serviceTypes}
                  personInsurances={personInsurances}
                  isLoadingInsurances={isLoadingInsurances}
                  isLoading={isLoading}
                  readOnly={isReadOnlyMode}
                />
              </div>

              <div className="space-y-5">
                {/* Datas */}
                <AppointmentDates
                  formData={formData}
                  setFormData={setFormData}
                  readOnly={isReadOnlyMode}
                />

                {/* Status do Agendamento (apenas para edição) */}
                {isEditMode && (
                  <AppointmentStatus
                    formData={formData}
                    setFormData={setFormData}
                    readOnly={isReadOnlyMode}
                  />
                )}

                {/* Agendamentos Sequenciais - apenas para novos agendamentos */}
                {!isEditMode && (
                  <div className="mb-8">
                    <SequentialAppointments
                      formData={formData}
                      setFormData={setFormData}
                      lastAppointmentTime={lastAppointmentTime}
                    />
                  </div>
                )}

                {/* Recorrência - apenas para novos agendamentos */}
                {!isEditMode && (
                  <RecurrenceSettings
                    formData={formData}
                    setFormData={setFormData}
                  />
                )}

                {/* Exibição de Limites do Convênio */}
                {formData.personId && formData.insuranceId && formData.serviceTypeId && (
                  <InsuranceLimitsDisplay
                    limitInfo={limitInfo}
                    isLoading={isCheckingLimits}
                    isEditMode={isEditMode}
                  />
                )}
              </div>
            </div>
          </div>

        </form>
      </ModuleModal>
    </>
  );
};

export default AppointmentModal;