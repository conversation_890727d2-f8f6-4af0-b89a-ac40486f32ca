"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/scheduler/appointments-report/page",{

/***/ "(app-pages-browser)/./src/components/calendar/AppointmentModal.js":
/*!*****************************************************!*\
  !*** ./src/components/calendar/AppointmentModal.js ***!
  \*****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AppointmentModal: () => (/* binding */ AppointmentModal),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _contexts_ToastContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/ToastContext */ \"(app-pages-browser)/./src/contexts/ToastContext.js\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.js\");\n/* harmony import */ var _barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=format!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/format.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _components_ui_ModuleModal__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/ModuleModal */ \"(app-pages-browser)/./src/components/ui/ModuleModal.js\");\n/* harmony import */ var _app_modules_scheduler_services_appointmentService__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/app/modules/scheduler/services/appointmentService */ \"(app-pages-browser)/./src/app/modules/scheduler/services/appointmentService.js\");\n/* harmony import */ var _app_modules_scheduler_services_insuranceLimitService__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/app/modules/scheduler/services/insuranceLimitService */ \"(app-pages-browser)/./src/app/modules/scheduler/services/insuranceLimitService.js\");\n/* harmony import */ var _app_modules_scheduler_services_insuranceServiceLimitService__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/app/modules/scheduler/services/insuranceServiceLimitService */ \"(app-pages-browser)/./src/app/modules/scheduler/services/insuranceServiceLimitService.js\");\n/* harmony import */ var _app_modules_people_services_insurancesService__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/app/modules/people/services/insurancesService */ \"(app-pages-browser)/./src/app/modules/people/services/insurancesService.js\");\n/* harmony import */ var _components_ui_ConfirmationDialog__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/ConfirmationDialog */ \"(app-pages-browser)/./src/components/ui/ConfirmationDialog.js\");\n/* harmony import */ var _appointmentModal_BasicInfoForm__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./appointmentModal/BasicInfoForm */ \"(app-pages-browser)/./src/components/calendar/appointmentModal/BasicInfoForm.js\");\n/* harmony import */ var _appointmentModal_SpecialtySelector__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./appointmentModal/SpecialtySelector */ \"(app-pages-browser)/./src/components/calendar/appointmentModal/SpecialtySelector.js\");\n/* harmony import */ var _appointmentModal_AppointmentSelectors__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./appointmentModal/AppointmentSelectors */ \"(app-pages-browser)/./src/components/calendar/appointmentModal/AppointmentSelectors.js\");\n/* harmony import */ var _InsuranceLimitsDisplay__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ./InsuranceLimitsDisplay */ \"(app-pages-browser)/./src/components/calendar/InsuranceLimitsDisplay.js\");\n/* harmony import */ var _appointmentModal_AppointmentDates__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! ./appointmentModal/AppointmentDates */ \"(app-pages-browser)/./src/components/calendar/appointmentModal/AppointmentDates.js\");\n/* harmony import */ var _appointmentModal_AppointmentStatus__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! ./appointmentModal/AppointmentStatus */ \"(app-pages-browser)/./src/components/calendar/appointmentModal/AppointmentStatus.js\");\n/* harmony import */ var _appointmentModal_SequentialAppointments__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! ./appointmentModal/SequentialAppointments */ \"(app-pages-browser)/./src/components/calendar/appointmentModal/SequentialAppointments.js\");\n/* harmony import */ var _appointmentModal_RecurrenceSettings__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! ./appointmentModal/RecurrenceSettings */ \"(app-pages-browser)/./src/components/calendar/appointmentModal/RecurrenceSettings.js\");\n/* harmony import */ var _appointmentModal_ModuleModalFooter__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! ./appointmentModal/ModuleModalFooter */ \"(app-pages-browser)/./src/components/calendar/appointmentModal/ModuleModalFooter.js\");\n/* __next_internal_client_entry_do_not_use__ AppointmentModal,default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n// import { utcToLocal, localToUTC } from \"@/utils/dateFormatters\";\n\n\n\n\n\n\n\n// Importando os componentes refatorados\n\n\n\n\n\n\n\n\n\nconst AppointmentModal = (param)=>{\n    let { isOpen, onClose, selectedDate, selectedAppointment, onAppointmentChange, checkAvailability, canCreate = false, canEdit = false, canDelete = false } = param;\n    _s();\n    // Função para forçar o fechamento do modal\n    const forceCloseModal = ()=>{\n        // Resetar TODOS os estados que podem estar impedindo o fechamento\n        setConfirmationDialogOpen(false);\n        // Removido setCanCloseMainModal pois não existe mais\n        setIsLoading(false);\n        // Chamar onClose diretamente\n        onClose();\n    };\n    const { toast_success, toast_error } = (0,_contexts_ToastContext__WEBPACK_IMPORTED_MODULE_2__.useToast)();\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        title: \"\",\n        description: \"\",\n        specialty: \"\",\n        providerId: \"\",\n        personId: \"\",\n        locationId: \"\",\n        serviceTypeId: \"\",\n        insuranceId: \"\",\n        startDate: null,\n        endDate: null,\n        sequentialAppointments: 1,\n        status: \"PENDING\",\n        recurrence: {\n            enabled: false,\n            type: \"OCCURRENCES\",\n            numberOfOccurrences: 1,\n            endDate: null,\n            patterns: []\n        }\n    });\n    const [providers, setProviders] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [filteredProviders, setFilteredProviders] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [specialties, setSpecialties] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [persons, setPersons] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [locations, setLocations] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [serviceTypes, setServiceTypes] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [personInsurances, setPersonInsurances] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoadingInsurances, setIsLoadingInsurances] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isCheckingLimits, setIsCheckingLimits] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [limitInfo, setLimitInfo] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [confirmationDialogOpen, setConfirmationDialogOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [actionToConfirm, setActionToConfirm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Removido canCloseMainModal pois não é mais necessário\n    // Efeito para pré-carregar dados quando o modal é aberto com um agendamento existente\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AppointmentModal.useEffect\": ()=>{\n            if (isOpen && selectedAppointment) {\n                console.log(\"Modal aberto com agendamento existente, pré-carregando dados\");\n                console.log(\"Dados completos do agendamento selecionado:\", selectedAppointment);\n                // Verificar se o ID está definido\n                if (!selectedAppointment.id || selectedAppointment.id === \"undefined\") {\n                    console.error(\"ERRO: ID do agendamento inválido!\", selectedAppointment.id);\n                    return;\n                }\n                // Verificar se temos o personId\n                if (!selectedAppointment.personId) {\n                    console.warn(\"ALERTA: personId não encontrado no agendamento selecionado!\");\n                    // Não temos personId, mas não vamos buscar da API novamente\n                    // Apenas exibir um aviso e continuar com os dados que temos\n                    console.log(\"Continuando com os dados disponíveis sem personId\");\n                }\n                // Carregar convênios para o paciente se tivermos personId\n                if (selectedAppointment.personId) {\n                    loadPersonInsurances(selectedAppointment.personId, selectedAppointment.insuranceId);\n                    // Carregar tipos de serviço se tivermos convênio\n                    if (selectedAppointment.insuranceId) {\n                        loadServiceTypes(selectedAppointment.personId, selectedAppointment.insuranceId, selectedAppointment.serviceTypeId);\n                    }\n                }\n            }\n        }\n    }[\"AppointmentModal.useEffect\"], [\n        isOpen,\n        selectedAppointment\n    ]);\n    // Função para carregar convênios do paciente\n    const loadPersonInsurances = (personId, insuranceId)=>{\n        if (!personId) return;\n        console.log(\"Carregando conv\\xeanios para o paciente ID: \".concat(personId));\n        setIsLoadingInsurances(true);\n        _app_modules_people_services_insurancesService__WEBPACK_IMPORTED_MODULE_8__[\"default\"].listPersonInsurances(personId).then((insurances)=>{\n            console.log(\"Pr\\xe9-carregados \".concat((insurances === null || insurances === void 0 ? void 0 : insurances.length) || 0, \" conv\\xeanios para o paciente \").concat(personId));\n            setPersonInsurances(insurances || []);\n            // Verificar se o convênio do agendamento está na lista\n            if (insuranceId && insurances && insurances.length > 0) {\n                const insuranceExists = insurances.some((ins)=>(ins.insuranceId || ins.id) === insuranceId);\n                if (!insuranceExists) {\n                    console.log(\"Conv\\xeanio \".concat(insuranceId, \" n\\xe3o encontrado na lista, adicionando manualmente\"));\n                    // Adicionar o convênio manualmente à lista\n                    if (selectedAppointment.insurance) {\n                        const manualInsurance = {\n                            id: insuranceId,\n                            insuranceId: insuranceId,\n                            name: selectedAppointment.insurance.name || \"Convênio\",\n                            insurance: selectedAppointment.insurance\n                        };\n                        setPersonInsurances((prev)=>[\n                                ...prev,\n                                manualInsurance\n                            ]);\n                    } else {\n                        // Tentar buscar o convênio do backend\n                        _app_modules_people_services_insurancesService__WEBPACK_IMPORTED_MODULE_8__[\"default\"].getInsuranceById(insuranceId).then((insuranceData)=>{\n                            if (insuranceData) {\n                                const manualInsurance = {\n                                    id: insuranceId,\n                                    insuranceId: insuranceId,\n                                    name: insuranceData.name || \"Convênio\",\n                                    insurance: insuranceData\n                                };\n                                setPersonInsurances((prev)=>[\n                                        ...prev,\n                                        manualInsurance\n                                    ]);\n                            }\n                        }).catch((error)=>{\n                            console.error(\"Erro ao buscar convênio do backend:\", error);\n                        });\n                    }\n                }\n            }\n        }).catch((error)=>{\n            console.error(\"Erro ao pré-carregar convênios:\", error);\n        }).finally(()=>{\n            setIsLoadingInsurances(false);\n        });\n    };\n    // Função para carregar tipos de serviço\n    const loadServiceTypes = (personId, insuranceId, serviceTypeId)=>{\n        if (!personId || !insuranceId) return;\n        console.log(\"Carregando tipos de servi\\xe7o para o paciente ID: \".concat(personId, \" e conv\\xeanio ID: \").concat(insuranceId));\n        setIsLoading(true);\n        _app_modules_scheduler_services_insuranceServiceLimitService__WEBPACK_IMPORTED_MODULE_7__[\"default\"].getServiceTypesByInsurance(insuranceId, personId).then((serviceTypes)=>{\n            console.log(\"Pr\\xe9-carregados \".concat((serviceTypes === null || serviceTypes === void 0 ? void 0 : serviceTypes.length) || 0, \" tipos de servi\\xe7o\"));\n            setServiceTypes(serviceTypes || []);\n            // Verificar se o tipo de serviço do agendamento está na lista\n            if (serviceTypeId && serviceTypes && serviceTypes.length > 0) {\n                const serviceTypeExists = serviceTypes.some((type)=>type.id === serviceTypeId);\n                if (!serviceTypeExists) {\n                    console.log(\"Tipo de servi\\xe7o \".concat(serviceTypeId, \" n\\xe3o encontrado na lista, adicionando manualmente\"));\n                    // Adicionar o tipo de serviço manualmente à lista\n                    if (selectedAppointment.serviceType) {\n                        const manualServiceType = {\n                            id: serviceTypeId,\n                            name: selectedAppointment.serviceType.name || \"Tipo de Serviço\",\n                            value: selectedAppointment.serviceType.value || \"0\"\n                        };\n                        setServiceTypes((prev)=>[\n                                ...prev,\n                                manualServiceType\n                            ]);\n                    } else {\n                        // Tentar buscar o tipo de serviço do backend\n                        _app_modules_scheduler_services_insuranceServiceLimitService__WEBPACK_IMPORTED_MODULE_7__[\"default\"].getServiceTypeById(serviceTypeId).then((serviceTypeData)=>{\n                            if (serviceTypeData) {\n                                const manualServiceType = {\n                                    id: serviceTypeId,\n                                    name: serviceTypeData.name || \"Tipo de Serviço\",\n                                    value: serviceTypeData.value || \"0\"\n                                };\n                                setServiceTypes((prev)=>[\n                                        ...prev,\n                                        manualServiceType\n                                    ]);\n                            }\n                        }).catch((error)=>{\n                            console.error(\"Erro ao buscar tipo de serviço do backend:\", error);\n                        });\n                    }\n                }\n            }\n        }).catch((error)=>{\n            console.error(\"Erro ao pré-carregar tipos de serviço:\", error);\n        }).finally(()=>{\n            setIsLoading(false);\n        });\n    };\n    // Garantir que o modal possa ser fechado quando isOpen mudar para false\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AppointmentModal.useEffect\": ()=>{\n            if (!isOpen) {\n                // Resetar TODOS os estados que podem estar impedindo o fechamento\n                setConfirmationDialogOpen(false);\n                setIsLoading(false);\n                // Limpar formulário\n                setFormData({\n                    title: \"\",\n                    description: \"\",\n                    specialty: \"\",\n                    providerId: \"\",\n                    personId: \"\",\n                    locationId: \"\",\n                    serviceTypeId: \"\",\n                    insuranceId: \"\",\n                    startDate: null,\n                    endDate: null,\n                    sequentialAppointments: 1,\n                    status: \"PENDING\",\n                    recurrence: {\n                        enabled: false,\n                        type: \"OCCURRENCES\",\n                        numberOfOccurrences: 1,\n                        endDate: null,\n                        patterns: []\n                    }\n                });\n            }\n        }\n    }[\"AppointmentModal.useEffect\"], [\n        isOpen\n    ]);\n    // Verificar se o modal deve exibir modo de edição ou criação\n    const isEditMode = !!selectedAppointment;\n    // Verificar se o usuário tem permissão para a ação atual\n    const hasPermission = isEditMode ? canEdit : canCreate;\n    // Verificar se o usuário é um cliente\n    const { user } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    const isClient = user === null || user === void 0 ? void 0 : user.isClient;\n    // Modo somente leitura para clientes\n    const isReadOnlyMode = isClient && isEditMode;\n    // Se não tiver permissão e não for cliente em modo somente leitura, exibir mensagem\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AppointmentModal.useEffect\": ()=>{\n            if (isOpen && !hasPermission && !isReadOnlyMode) {\n                toast_error(isEditMode ? \"Você não tem permissão para editar agendamentos.\" : \"Você não tem permissão para criar agendamentos.\");\n            }\n        }\n    }[\"AppointmentModal.useEffect\"], [\n        isOpen,\n        hasPermission,\n        isEditMode,\n        isReadOnlyMode,\n        toast_error\n    ]);\n    // Função para extrair especialidades únicas dos provedores\n    const extractSpecialties = (providersList)=>{\n        const uniqueSpecialties = [\n            ...new Set(providersList.filter((provider)=>{\n                var _provider_professionObj;\n                return provider && (provider.profession || ((_provider_professionObj = provider.professionObj) === null || _provider_professionObj === void 0 ? void 0 : _provider_professionObj.name));\n            }).map((provider)=>{\n                var _provider_professionObj;\n                return provider.profession || ((_provider_professionObj = provider.professionObj) === null || _provider_professionObj === void 0 ? void 0 : _provider_professionObj.name);\n            }))\n        ].sort();\n        return uniqueSpecialties;\n    };\n    // Filtrar provedores baseado na especialidade selecionada\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AppointmentModal.useEffect\": ()=>{\n            if (formData.specialty) {\n                const filtered = providers.filter({\n                    \"AppointmentModal.useEffect.filtered\": (provider)=>{\n                        var _provider_professionObj;\n                        const providerProfession = provider.profession || ((_provider_professionObj = provider.professionObj) === null || _provider_professionObj === void 0 ? void 0 : _provider_professionObj.name);\n                        return providerProfession === formData.specialty;\n                    }\n                }[\"AppointmentModal.useEffect.filtered\"]);\n                setFilteredProviders(filtered);\n                // Se o provedor atualmente selecionado não estiver nesta especialidade, limpe a seleção\n                if (formData.providerId && !filtered.some({\n                    \"AppointmentModal.useEffect\": (p)=>p.id === formData.providerId\n                }[\"AppointmentModal.useEffect\"])) {\n                    setFormData({\n                        \"AppointmentModal.useEffect\": (prev)=>({\n                                ...prev,\n                                providerId: \"\"\n                            })\n                    }[\"AppointmentModal.useEffect\"]);\n                }\n            } else {\n                setFilteredProviders(providers);\n            }\n        }\n    }[\"AppointmentModal.useEffect\"], [\n        formData.specialty,\n        providers,\n        formData.providerId\n    ]);\n    // Carregar dados para os selects\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AppointmentModal.useEffect\": ()=>{\n            const loadData = {\n                \"AppointmentModal.useEffect.loadData\": async ()=>{\n                    setIsLoading(true);\n                    try {\n                        console.log(\"Loading appointment modal data...\");\n                        // Load data separately to better debug any issues\n                        console.log(\"Loading providers...\");\n                        const providersRes = await _app_modules_scheduler_services_appointmentService__WEBPACK_IMPORTED_MODULE_5__.appointmentService.getProviders();\n                        console.log(\"Loaded \".concat((providersRes === null || providersRes === void 0 ? void 0 : providersRes.length) || 0, \" providers\"));\n                        console.log(\"Loading persons...\");\n                        const personsRes = await _app_modules_scheduler_services_appointmentService__WEBPACK_IMPORTED_MODULE_5__.appointmentService.getPersons();\n                        console.log(\"Loaded \".concat((personsRes === null || personsRes === void 0 ? void 0 : personsRes.length) || 0, \" persons\"));\n                        console.log(\"Loading locations...\");\n                        const locationsRes = await _app_modules_scheduler_services_appointmentService__WEBPACK_IMPORTED_MODULE_5__.appointmentService.getLocations();\n                        console.log(\"Loaded \".concat((locationsRes === null || locationsRes === void 0 ? void 0 : locationsRes.length) || 0, \" locations\"));\n                        // Set state with valid data only\n                        const validProviders = providersRes || [];\n                        setProviders(validProviders);\n                        setFilteredProviders(validProviders);\n                        setPersons(personsRes || []);\n                        // Extract and set unique specialties\n                        const extractedSpecialties = extractSpecialties(validProviders);\n                        setSpecialties(extractedSpecialties);\n                        setLocations(locationsRes || []);\n                        // Log what was set\n                        console.log(\"Set providers:\", (validProviders === null || validProviders === void 0 ? void 0 : validProviders.length) || 0);\n                        console.log(\"Set specialties:\", (extractedSpecialties === null || extractedSpecialties === void 0 ? void 0 : extractedSpecialties.length) || 0);\n                        console.log(\"Set persons:\", (personsRes === null || personsRes === void 0 ? void 0 : personsRes.length) || 0);\n                        console.log(\"Set locations:\", (locationsRes === null || locationsRes === void 0 ? void 0 : locationsRes.length) || 0);\n                    } catch (error) {\n                        console.error(\"Erro ao carregar dados:\", error);\n                        toast_error(\"Erro ao carregar dados necessários. Por favor, tente novamente.\");\n                    } finally{\n                        setIsLoading(false);\n                    }\n                }\n            }[\"AppointmentModal.useEffect.loadData\"];\n            if (isOpen) {\n                loadData();\n            }\n        }\n    }[\"AppointmentModal.useEffect\"], [\n        isOpen\n    ]);\n    // Carregar os convênios do paciente selecionado\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AppointmentModal.useEffect\": ()=>{\n            const loadPersonInsurances = {\n                \"AppointmentModal.useEffect.loadPersonInsurances\": async ()=>{\n                    // Se não tiver um personId (paciente) selecionado, não fazer nada\n                    if (!formData.personId) {\n                        setPersonInsurances([]);\n                        return;\n                    }\n                    setIsLoadingInsurances(true);\n                    try {\n                        console.log(\"Carregando conv\\xeanios do paciente ID: \".concat(formData.personId));\n                        const insurancesRes = await _app_modules_people_services_insurancesService__WEBPACK_IMPORTED_MODULE_8__[\"default\"].listPersonInsurances(formData.personId);\n                        if (insurancesRes && insurancesRes.length > 0) {\n                            console.log(\"Carregados \".concat(insurancesRes.length, \" conv\\xeanios do paciente\"));\n                            console.log(\"Primeiro convênio:\", insurancesRes[0]);\n                            setPersonInsurances(insurancesRes);\n                            // Se estamos editando um agendamento existente e o convênio não está na lista,\n                            // não limpar o convênio selecionado\n                            if (formData.id && formData.insuranceId) {\n                                const insuranceExists = insurancesRes.some({\n                                    \"AppointmentModal.useEffect.loadPersonInsurances.insuranceExists\": (ins)=>(ins.insuranceId || ins.id) === formData.insuranceId\n                                }[\"AppointmentModal.useEffect.loadPersonInsurances.insuranceExists\"]);\n                                if (!insuranceExists) {\n                                    console.log(\"Conv\\xeanio \".concat(formData.insuranceId, \" n\\xe3o encontrado na lista, mas mantendo-o selecionado para edi\\xe7\\xe3o\"));\n                                }\n                            }\n                        // Removido o reset do convênio para novos agendamentos\n                        } else {\n                            console.warn(\"Nenhum convênio encontrado para este paciente\");\n                        // Manter o convênio selecionado mesmo se não houver convênios disponíveis\n                        // Isso permite que o usuário selecione \"Sem convênio\" manualmente\n                        }\n                    } catch (error) {\n                        console.error(\"Erro ao carregar conv\\xeanios do paciente \".concat(formData.personId, \":\"), error);\n                    } finally{\n                        setIsLoadingInsurances(false);\n                    }\n                }\n            }[\"AppointmentModal.useEffect.loadPersonInsurances\"];\n            // Carregar os convênios sempre que o paciente mudar\n            loadPersonInsurances();\n        }\n    }[\"AppointmentModal.useEffect\"], [\n        formData.personId,\n        formData.id,\n        formData.insuranceId\n    ]);\n    // Preencher dados do formulário baseado no evento selecionado ou data\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AppointmentModal.useEffect\": ()=>{\n            if (selectedAppointment) {\n                var _provider_professionObj;\n                console.log(\"Selected appointment:\", selectedAppointment);\n                // Verificar se o ID está definido\n                if (!selectedAppointment.id) {\n                    console.error(\"ERRO: ID do agendamento não definido!\");\n                    return;\n                }\n                // Buscar especialidade do provedor selecionado\n                const provider = providers.find({\n                    \"AppointmentModal.useEffect.provider\": (p)=>p.id === selectedAppointment.providerId\n                }[\"AppointmentModal.useEffect.provider\"]);\n                const providerSpecialty = (provider === null || provider === void 0 ? void 0 : provider.profession) || (provider === null || provider === void 0 ? void 0 : (_provider_professionObj = provider.professionObj) === null || _provider_professionObj === void 0 ? void 0 : _provider_professionObj.name) || \"\";\n                // Edição de agendamento existente\n                // Usar setTimeout para garantir que este efeito seja executado após os outros efeitos\n                // que podem estar tentando limpar os dados\n                setTimeout({\n                    \"AppointmentModal.useEffect\": ()=>{\n                        console.log(\"Preenchendo formulário com dados do agendamento selecionado\");\n                        console.log(\"Dados do agendamento:\", {\n                            id: selectedAppointment.id,\n                            personId: selectedAppointment.personId,\n                            insuranceId: selectedAppointment.insuranceId,\n                            serviceTypeId: selectedAppointment.serviceTypeId\n                        });\n                        // Garantir que temos valores válidos para todos os campos\n                        const formDataValues = {\n                            id: selectedAppointment.id,\n                            title: selectedAppointment.title || \"\",\n                            description: selectedAppointment.description || \"\",\n                            specialty: providerSpecialty || \"\",\n                            providerId: selectedAppointment.providerId || \"\",\n                            personId: selectedAppointment.personId || \"\",\n                            locationId: selectedAppointment.locationId || \"\",\n                            serviceTypeId: selectedAppointment.serviceTypeId || \"\",\n                            insuranceId: selectedAppointment.insuranceId || \"\",\n                            startDate: selectedAppointment.startDate || new Date().toISOString(),\n                            endDate: selectedAppointment.endDate || new Date(Date.now() + 3600000).toISOString(),\n                            sequentialAppointments: 1,\n                            status: selectedAppointment.status || \"PENDING\",\n                            recurrence: {\n                                enabled: false,\n                                type: \"OCCURRENCES\",\n                                numberOfOccurrences: 1,\n                                endDate: null,\n                                patterns: []\n                            }\n                        };\n                        console.log(\"Valores finais do formulário:\", formDataValues);\n                        setFormData(formDataValues);\n                    }\n                }[\"AppointmentModal.useEffect\"], 100); // Pequeno delay para garantir a ordem correta de execução\n            } else if (selectedDate) {\n                var _selectedDate_start, _selectedDate_end;\n                // Novo agendamento\n                // Usar os objetos Date diretamente, mantendo o horário local que o usuário clicou\n                console.log(\"[MODAL-DEBUG] selectedDate recebido:\", {\n                    start: (_selectedDate_start = selectedDate.start) === null || _selectedDate_start === void 0 ? void 0 : _selectedDate_start.toLocaleString(),\n                    end: (_selectedDate_end = selectedDate.end) === null || _selectedDate_end === void 0 ? void 0 : _selectedDate_end.toLocaleString(),\n                    temEnd: !!selectedDate.end,\n                    tipo: typeof selectedDate\n                });\n                // Para a data de início, usar o objeto Date do calendário diretamente\n                const startDate = selectedDate.start;\n                // Para a data de término, SEMPRE usar duração de 1 hora, ignorando o valor de end do calendário\n                console.log(\"[MODAL-DEBUG] FOR\\xc7ANDO endDate com dura\\xe7\\xe3o de 60 minutos, ignorando o valor de end\");\n                const endDate = new Date(selectedDate.start.getTime() + 60 * 60 * 1000);\n                console.log(\"[MODAL-DEBUG] Dura\\xe7\\xe3o for\\xe7ada (minutos): \".concat((endDate - startDate) / (60 * 1000)));\n                console.log(\"[MODAL-INIT] Datas do calend\\xe1rio (hor\\xe1rio local) - Start: \".concat(startDate.toLocaleString(), \", End: \").concat(endDate.toLocaleString()));\n                // Converter para strings ISO apenas para armazenamento no formData\n                // Importante: toISOString() converte para UTC, mas vamos usar isso apenas para armazenamento interno\n                const startISOString = startDate.toISOString();\n                const endISOString = endDate.toISOString();\n                setFormData({\n                    title: \"\",\n                    description: \"\",\n                    specialty: \"\",\n                    providerId: \"\",\n                    personId: \"\",\n                    locationId: \"\",\n                    serviceTypeId: \"\",\n                    insuranceId: \"\",\n                    startDate: startISOString,\n                    endDate: endISOString,\n                    sequentialAppointments: 1,\n                    status: \"PENDING\",\n                    recurrence: {\n                        enabled: false,\n                        type: \"OCCURRENCES\",\n                        numberOfOccurrences: 1,\n                        endDate: null,\n                        patterns: []\n                    }\n                });\n            }\n        }\n    }[\"AppointmentModal.useEffect\"], [\n        selectedAppointment,\n        selectedDate,\n        providers\n    ]);\n    // Carregar tipos de serviço com base no convênio selecionado ou todos os tipos se não houver convênio\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AppointmentModal.useEffect\": ()=>{\n            const loadServiceTypes = {\n                \"AppointmentModal.useEffect.loadServiceTypes\": async ()=>{\n                    // Se não tiver paciente, limpar tipos de serviço e retornar\n                    if (!formData.personId) {\n                        setServiceTypes([]);\n                        // Se não estamos editando um agendamento existente, resetar apenas o tipo de serviço\n                        if (!formData.id && formData.serviceTypeId) {\n                            setFormData({\n                                \"AppointmentModal.useEffect.loadServiceTypes\": (prev)=>({\n                                        ...prev,\n                                        serviceTypeId: \"\"\n                                    })\n                            }[\"AppointmentModal.useEffect.loadServiceTypes\"]);\n                        }\n                        return;\n                    }\n                    setIsLoading(true);\n                    try {\n                        let serviceTypesData = [];\n                        if (formData.insuranceId) {\n                            // Se há convênio, buscar tipos de serviço com limites configurados\n                            console.log(\"Carregando tipos de servi\\xe7o para o paciente ID: \".concat(formData.personId, \" e conv\\xeanio ID: \").concat(formData.insuranceId));\n                            serviceTypesData = await _app_modules_scheduler_services_insuranceServiceLimitService__WEBPACK_IMPORTED_MODULE_7__[\"default\"].getServiceTypesByInsurance(formData.insuranceId, formData.personId);\n                            console.log(\"Encontrados \".concat((serviceTypesData === null || serviceTypesData === void 0 ? void 0 : serviceTypesData.length) || 0, \" tipos de servi\\xe7o com limites configurados\"));\n                        } else {\n                            // Se não há convênio (agendamento particular), buscar todos os tipos de serviço da empresa\n                            console.log(\"Carregando todos os tipos de servi\\xe7o para agendamento particular\");\n                            // Importar o serviço de tipos de serviço\n                            const { serviceTypeService } = await __webpack_require__.e(/*! import() */ \"_app-pages-browser_src_app_modules_scheduler_services_serviceTypeService_js\").then(__webpack_require__.bind(__webpack_require__, /*! @/app/modules/scheduler/services/serviceTypeService */ \"(app-pages-browser)/./src/app/modules/scheduler/services/serviceTypeService.js\"));\n                            const response = await serviceTypeService.getServiceTypes();\n                            serviceTypesData = response.serviceTypes || response.data || [];\n                            console.log(\"Encontrados \".concat((serviceTypesData === null || serviceTypesData === void 0 ? void 0 : serviceTypesData.length) || 0, \" tipos de servi\\xe7o dispon\\xedveis\"));\n                        }\n                        if (serviceTypesData && serviceTypesData.length > 0) {\n                            setServiceTypes(serviceTypesData);\n                            // Se estamos editando um agendamento existente e o tipo de serviço não está na lista,\n                            // adicionar o tipo de serviço atual à lista\n                            if (formData.id && formData.serviceTypeId) {\n                                const serviceTypeExists = serviceTypesData.some({\n                                    \"AppointmentModal.useEffect.loadServiceTypes.serviceTypeExists\": (type)=>type.id === formData.serviceTypeId\n                                }[\"AppointmentModal.useEffect.loadServiceTypes.serviceTypeExists\"]);\n                                if (!serviceTypeExists && (selectedAppointment === null || selectedAppointment === void 0 ? void 0 : selectedAppointment.serviceType)) {\n                                    console.log(\"Tipo de servi\\xe7o \".concat(formData.serviceTypeId, \" n\\xe3o encontrado na lista, adicionando \\xe0 lista\"));\n                                    const currentServiceType = {\n                                        id: formData.serviceTypeId,\n                                        name: selectedAppointment.serviceType.name || \"Tipo de Serviço\",\n                                        value: selectedAppointment.serviceType.value || \"0\"\n                                    };\n                                    setServiceTypes({\n                                        \"AppointmentModal.useEffect.loadServiceTypes\": (prev)=>[\n                                                ...prev,\n                                                currentServiceType\n                                            ]\n                                    }[\"AppointmentModal.useEffect.loadServiceTypes\"]);\n                                }\n                            }\n                        } else {\n                            console.warn(\"Nenhum tipo de serviço encontrado\");\n                            setServiceTypes([]);\n                            // Se não estamos editando e temos um tipo de serviço selecionado, limpar apenas o tipo de serviço\n                            if (!formData.id && formData.serviceTypeId) {\n                                setFormData({\n                                    \"AppointmentModal.useEffect.loadServiceTypes\": (prev)=>({\n                                            ...prev,\n                                            serviceTypeId: \"\"\n                                        })\n                                }[\"AppointmentModal.useEffect.loadServiceTypes\"]);\n                            }\n                            // Exibir mensagem apenas se houver convênio mas não houver tipos de serviço\n                            if (formData.insuranceId) {\n                                toast_error(\"Não há tipos de serviço configurados para este paciente e convênio. \" + \"Por favor, configure os limites de serviço para esta combinação de paciente e convênio.\");\n                            }\n                        }\n                    } catch (error) {\n                        console.error(\"Erro ao carregar tipos de serviço:\", error);\n                        setServiceTypes([]);\n                        toast_error(\"Erro ao carregar tipos de serviço. Por favor, tente novamente.\");\n                    } finally{\n                        setIsLoading(false);\n                    }\n                }\n            }[\"AppointmentModal.useEffect.loadServiceTypes\"];\n            loadServiceTypes();\n        }\n    }[\"AppointmentModal.useEffect\"], [\n        formData.insuranceId,\n        formData.personId,\n        formData.id,\n        formData.serviceTypeId,\n        selectedAppointment,\n        toast_error\n    ]);\n    // Verificar limites de convênio\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AppointmentModal.useEffect\": ()=>{\n            const checkInsuranceLimits = {\n                \"AppointmentModal.useEffect.checkInsuranceLimits\": async ()=>{\n                    // Só verifica se tiver pessoa, convênio e tipo de serviço selecionados\n                    if (formData.personId && formData.insuranceId && formData.serviceTypeId && formData.startDate) {\n                        setIsCheckingLimits(true);\n                        try {\n                            console.log(\"Verificando limites para data do agendamento: \".concat(formData.startDate));\n                            const result = await _app_modules_scheduler_services_insuranceLimitService__WEBPACK_IMPORTED_MODULE_6__[\"default\"].checkLimitAvailability({\n                                personId: formData.personId,\n                                insuranceId: formData.insuranceId,\n                                serviceTypeId: formData.serviceTypeId,\n                                appointmentDate: new Date(formData.startDate) // Passar a data do agendamento\n                            });\n                            console.log(\"Resultado da verifica\\xe7\\xe3o de limites:\", result);\n                            setLimitInfo(result.usage);\n                            // Se limite foi atingido, mostrar erro apenas para novos agendamentos\n                            // Para edição, apenas mostrar um aviso informativo\n                            if (!result.available) {\n                                if (!isEditMode) {\n                                    toast_error(result.message);\n                                } else {\n                                    // Para edição, mostrar um aviso mais suave\n                                    console.log(\"Limite atingido, mas permitindo edição do agendamento existente\");\n                                }\n                            }\n                        } catch (error) {\n                            console.error(\"Erro ao verificar limites:\", error);\n                        } finally{\n                            setIsCheckingLimits(false);\n                        }\n                    } else {\n                        // Limpar dados de limite se algum campo necessário não estiver preenchido\n                        setLimitInfo(null);\n                    }\n                }\n            }[\"AppointmentModal.useEffect.checkInsuranceLimits\"];\n            checkInsuranceLimits();\n        }\n    }[\"AppointmentModal.useEffect\"], [\n        formData.personId,\n        formData.insuranceId,\n        formData.serviceTypeId,\n        formData.startDate,\n        toast_error,\n        isEditMode\n    ]);\n    // Validar agendamento antes de enviar\n    const validateAppointment = async function() {\n        let data = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : formData;\n        // Validação básica\n        if (!data.title) {\n            toast_error(\"Por favor, informe um título para o agendamento.\");\n            return false;\n        }\n        if (!data.providerId) {\n            toast_error(\"Por favor, selecione um profissional.\");\n            return false;\n        }\n        if (!data.personId) {\n            toast_error(\"Por favor, selecione um paciente.\");\n            return false;\n        }\n        if (!data.locationId) {\n            toast_error(\"Por favor, selecione um local.\");\n            return false;\n        }\n        if (!data.serviceTypeId) {\n            toast_error(\"Por favor, selecione um tipo de serviço.\");\n            return false;\n        }\n        // Verificar limites de convênio apenas para novos agendamentos\n        // Para edição, permitir mesmo que o limite tenha sido atingido\n        if (!isEditMode && limitInfo && limitInfo.monthly && !limitInfo.monthly.unlimited) {\n            const isLimitReached = limitInfo.monthly.used >= limitInfo.monthly.limit;\n            if (isLimitReached) {\n                console.log(\"Limite de convênio atingido, bloqueando criação de novo agendamento\");\n                toast_error(\"Limite mensal de \".concat(limitInfo.monthly.limit, \" agendamentos atingido. Voc\\xea j\\xe1 utilizou \").concat(limitInfo.monthly.used, \" agendamentos.\"));\n                return false;\n            }\n        }\n        // Verificar se há conflitos com agendamentos existentes\n        try {\n            // Verificar disponibilidade tanto para novos agendamentos quanto para edição\n            // Verificar se o horário está disponível para o profissional no servidor\n            const providerCheckResponse = await _app_modules_scheduler_services_appointmentService__WEBPACK_IMPORTED_MODULE_5__.appointmentService.checkAvailability({\n                providerId: data.providerId,\n                startDate: data.startDate,\n                endDate: data.endDate,\n                excludeId: data.id // Para edição, excluir o próprio agendamento\n            });\n            if (!providerCheckResponse.available) {\n                if (providerCheckResponse.conflict) {\n                    toast_error(\"Horário indisponível para o profissional selecionado.\");\n                } else {\n                    toast_error(\"Horário indisponível para o profissional.\");\n                }\n                return false;\n            }\n            // Verificar se o paciente já tem um agendamento no mesmo horário\n            const patientCheckResponse = await _app_modules_scheduler_services_appointmentService__WEBPACK_IMPORTED_MODULE_5__.appointmentService.checkPatientAvailability({\n                personId: data.personId,\n                startDate: data.startDate,\n                endDate: data.endDate,\n                excludeId: data.id // Para edição, excluir o próprio agendamento\n            });\n            if (!patientCheckResponse.available) {\n                if (patientCheckResponse.conflict) {\n                    toast_error(\"Paciente já possui um agendamento neste horário.\");\n                } else {\n                    toast_error(\"Horário indisponível para o paciente.\");\n                }\n                return false;\n            }\n        } catch (error) {\n            console.error(\"Erro ao verificar disponibilidade no servidor:\", error);\n        // Não bloquear o fluxo em caso de erro na verificação\n        }\n        // Validar recorrência, se estiver habilitada (apenas para novos agendamentos)\n        if (!isEditMode && data.recurrence.enabled) {\n            // Validar padrões de recorrência\n            if (!data.recurrence.patterns || data.recurrence.patterns.length === 0) {\n                toast_error(\"Por favor, selecione pelo menos um dia da semana para a recorrência.\");\n                return false;\n            }\n            // Validar data final ou número de ocorrências\n            if (data.recurrence.type === 'END_DATE') {\n                if (!data.recurrence.endDate) {\n                    toast_error(\"Por favor, informe uma data final para a recorrência.\");\n                    return false;\n                }\n                const endDate = new Date(data.recurrence.endDate);\n                const tomorrow = new Date();\n                tomorrow.setDate(tomorrow.getDate() + 1);\n                if (endDate <= tomorrow) {\n                    toast_error(\"A data final da recorrência deve ser pelo menos 1 dia após a data atual.\");\n                    return false;\n                }\n            } else if (data.recurrence.type === 'OCCURRENCES') {\n                if (!data.recurrence.numberOfOccurrences || data.recurrence.numberOfOccurrences < 1) {\n                    toast_error(\"Por favor, informe um número válido de ocorrências para a recorrência.\");\n                    return false;\n                }\n            }\n        }\n        // Verificar disponibilidade do profissional, se implementado\n        if (checkAvailability) {\n            const isAvailable = await checkAvailability(data.providerId, {\n                start: data.startDate,\n                end: data.endDate\n            });\n            if (!isAvailable) {\n                toast_error(\"O profissional selecionado não está disponível neste horário.\");\n                return false;\n            }\n            // Se estiver criando agendamentos sequenciais (apenas para novos agendamentos), verificar cada um deles\n            if (!isEditMode && data.sequentialAppointments > 1) {\n                // Converter strings ISO para objetos Date para cálculos\n                const startDate = new Date(data.startDate);\n                const endDate = new Date(data.endDate);\n                let currentEndDate = endDate;\n                // Duração fixa de 1 hora (3600000 ms) para agendamentos sequenciais\n                const sequentialDuration = 3600000; // 1 hora em milissegundos\n                console.log(\"[SEQUENTIAL] Verificando \".concat(data.sequentialAppointments, \" agendamentos sequenciais\"));\n                console.log(\"[SEQUENTIAL] Agendamento inicial: \".concat(startDate.toLocaleString(), \" - \").concat(endDate.toLocaleString()));\n                for(let i = 1; i < data.sequentialAppointments; i++){\n                    // Criar o próximo agendamento sequencial\n                    // Começa exatamente quando o anterior termina\n                    const nextStartDate = new Date(currentEndDate.getTime());\n                    // Duração fixa de 1 hora\n                    const nextEndDate = new Date(nextStartDate.getTime() + sequentialDuration);\n                    console.log(\"[SEQUENTIAL] Verificando agendamento #\".concat(i + 1, \": \").concat(nextStartDate.toLocaleString(), \" - \").concat(nextEndDate.toLocaleString()));\n                    // Converter para strings ISO para verificar disponibilidade\n                    const nextStartISO = nextStartDate.toISOString();\n                    const nextEndISO = nextEndDate.toISOString();\n                    // Verificar disponibilidade do profissional no servidor para este horário sequencial\n                    try {\n                        const providerServerCheckResponse = await _app_modules_scheduler_services_appointmentService__WEBPACK_IMPORTED_MODULE_5__.appointmentService.checkAvailability({\n                            providerId: data.providerId,\n                            startDate: nextStartISO,\n                            endDate: nextEndISO,\n                            excludeId: data.id // Para edição, excluir o próprio agendamento\n                        });\n                        // Se não estiver disponível no servidor, mostrar mensagem de erro\n                        if (!providerServerCheckResponse.available) {\n                            // Formatar as datas para exibição\n                            const formatTime = (date)=>(0,_barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_19__.format)(date, 'HH:mm');\n                            if (providerServerCheckResponse.conflict) {\n                                const conflict = providerServerCheckResponse.conflict;\n                                const conflictStart = new Date(conflict.startDate);\n                                const conflictEnd = new Date(conflict.endDate);\n                                const appointmentTitle = conflict.title || \"Sem título\";\n                                toast_error(\"O \".concat(i + 1, \"\\xba agendamento sequencial (\").concat(formatTime(nextStartDate), \" - \").concat(formatTime(nextEndDate), ') conflita com o agendamento \"').concat(appointmentTitle, '\" (').concat((0,_barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_19__.format)(conflictStart, 'HH:mm'), \" - \").concat((0,_barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_19__.format)(conflictEnd, 'HH:mm'), \").\"));\n                            } else {\n                                toast_error(\"O profissional n\\xe3o est\\xe1 dispon\\xedvel para o \".concat(i + 1, \"\\xba agendamento sequencial (\").concat(formatTime(nextStartDate), \" - \").concat(formatTime(nextEndDate), \").\"));\n                            }\n                            return false;\n                        }\n                        // Verificar disponibilidade do paciente no servidor para este horário sequencial\n                        const patientServerCheckResponse = await _app_modules_scheduler_services_appointmentService__WEBPACK_IMPORTED_MODULE_5__.appointmentService.checkPatientAvailability({\n                            personId: data.personId,\n                            startDate: nextStartISO,\n                            endDate: nextEndISO,\n                            excludeId: data.id // Para edição, excluir o próprio agendamento\n                        });\n                        // Se não estiver disponível no servidor, mostrar mensagem de erro\n                        if (!patientServerCheckResponse.available) {\n                            // Formatar as datas para exibição\n                            const formatTime = (date)=>(0,_barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_19__.format)(date, 'HH:mm');\n                            if (patientServerCheckResponse.conflict) {\n                                const conflict = patientServerCheckResponse.conflict;\n                                const conflictStart = new Date(conflict.startDate);\n                                const conflictEnd = new Date(conflict.endDate);\n                                const appointmentTitle = conflict.title || \"Sem título\";\n                                toast_error('O paciente j\\xe1 possui um agendamento \"'.concat(appointmentTitle, '\" (').concat((0,_barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_19__.format)(conflictStart, 'HH:mm'), \" - \").concat((0,_barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_19__.format)(conflictEnd, 'HH:mm'), \") que conflita com o \").concat(i + 1, \"\\xba agendamento sequencial (\").concat(formatTime(nextStartDate), \" - \").concat(formatTime(nextEndDate), \").\"));\n                            } else {\n                                toast_error(\"O paciente j\\xe1 possui um agendamento no hor\\xe1rio do \".concat(i + 1, \"\\xba agendamento sequencial (\").concat(formatTime(nextStartDate), \" - \").concat(formatTime(nextEndDate), \").\"));\n                            }\n                            return false;\n                        }\n                    } catch (error) {\n                        console.error(\"Erro ao verificar disponibilidade do \".concat(i + 1, \"\\xba agendamento sequencial:\"), error);\n                    // Continuar com a verificação local em caso de erro na verificação do servidor\n                    }\n                    // Verificar disponibilidade local (horários de trabalho)\n                    const isNextSlotAvailable = await checkAvailability(data.providerId, {\n                        start: nextStartISO,\n                        end: nextEndISO\n                    });\n                    if (!isNextSlotAvailable) {\n                        toast_error(\"O profissional n\\xe3o est\\xe1 dispon\\xedvel para o \".concat(i + 1, \"\\xba agendamento sequencial (\").concat((0,_barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_19__.format)(nextStartDate, 'HH:mm'), \" - \").concat((0,_barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_19__.format)(nextEndDate, 'HH:mm'), \").\"));\n                        return false;\n                    }\n                    currentEndDate = nextEndDate;\n                }\n            }\n        }\n        return true;\n    };\n    // Função para confirmar a ação antes de executar\n    const confirmAction = async ()=>{\n        if (!actionToConfirm) {\n            return;\n        }\n        console.log('[CONFIRM-ACTION] Confirmando ação:', actionToConfirm.type);\n        try {\n            // Fechar o diálogo de confirmação imediatamente\n            setConfirmationDialogOpen(false);\n            // Garantir que o modal principal possa ser fechado\n            if (actionToConfirm.type === \"save\") {\n                await saveAppointment();\n            } else if (actionToConfirm.type === \"delete\") {\n                await deleteAppointment();\n            }\n        } catch (error) {\n            console.error('[CONFIRM-ACTION] Erro ao executar ação:', error);\n        // Garantir que o modal possa ser fechado mesmo em caso de erro\n        } finally{\n            // Garantir que o modal possa ser fechado após a ação, independente do resultado\n            setTimeout(()=>{\n                console.log('[CONFIRM-ACTION] Garantindo que o modal possa ser fechado após a ação');\n            }, 500);\n        }\n    };\n    // Função para salvar o agendamento após confirmação\n    const saveAppointment = async ()=>{\n        setIsLoading(true);\n        try {\n            // As datas no formData estão em formato ISO string (UTC)\n            // Isso é exatamente o que o backend espera, então não precisamos converter\n            console.log(\"[SAVE] Datas originais (UTC) - Start: \".concat(formData.startDate, \", End: \").concat(formData.endDate));\n            // Converter para objetos Date para exibir no log o horário local\n            const startLocalDate = new Date(formData.startDate);\n            const endLocalDate = new Date(formData.endDate);\n            console.log(\"[SAVE] Datas locais - Start: \".concat(startLocalDate.toLocaleString(), \", End: \").concat(endLocalDate.toLocaleString()));\n            // Criar objeto base com dados comuns\n            const baseAppointmentData = {\n                title: formData.title,\n                description: formData.description,\n                userId: formData.providerId,\n                providerId: formData.providerId,\n                personId: formData.personId,\n                locationId: formData.locationId,\n                serviceTypeId: formData.serviceTypeId,\n                insuranceId: formData.insuranceId || null,\n                // Enviar as datas em formato ISO (UTC) para o backend\n                startDate: formData.startDate,\n                endDate: formData.endDate,\n                creatorId: formData.providerId,\n                status: formData.status\n            };\n            // Adicionar sequentialAppointments apenas para novos agendamentos\n            const appointmentData = isEditMode ? baseAppointmentData : {\n                ...baseAppointmentData,\n                sequentialAppointments: formData.sequentialAppointments\n            };\n            console.log(\"[SAVE] Enviando datas (UTC) - Start: \".concat(appointmentData.startDate, \", End: \").concat(appointmentData.endDate));\n            if (!isEditMode && formData.recurrence.enabled) {\n                // Preparar dados de recorrência\n                const recurrenceData = {\n                    ...appointmentData,\n                    recurrenceType: formData.recurrence.type,\n                    // Para o tipo OCCURRENCES, usar o número de ocorrências\n                    // Para o tipo END_DATE, usar a data final (já em formato ISO)\n                    recurrenceValue: formData.recurrence.type === \"OCCURRENCES\" ? formData.recurrence.numberOfOccurrences : formData.recurrence.endDate,\n                    // Mapear os padrões de recorrência\n                    patterns: formData.recurrence.patterns.map((pattern)=>{\n                        console.log(\"[RECURRENCE] Padr\\xe3o: dia \".concat(pattern.dayOfWeek, \", \").concat(pattern.startTime, \" - \").concat(pattern.endTime));\n                        return {\n                            dayOfWeek: pattern.dayOfWeek,\n                            startTime: pattern.startTime,\n                            endTime: pattern.endTime\n                        };\n                    })\n                };\n                console.log(\"[RECURRENCE] Criando recorr\\xeancia do tipo \".concat(recurrenceData.recurrenceType));\n                console.log(\"[RECURRENCE] Valor: \".concat(recurrenceData.recurrenceValue));\n                console.log(\"[RECURRENCE] Padr\\xf5es: \".concat(recurrenceData.patterns.length));\n                await _app_modules_scheduler_services_appointmentService__WEBPACK_IMPORTED_MODULE_5__.appointmentService.createRecurrence(recurrenceData);\n            } else {\n                if (formData.id) {\n                    // Verificar se o ID é válido antes de atualizar\n                    if (!formData.id || formData.id === \"undefined\") {\n                        console.error(\"[SAVE] ID do agendamento inválido para atualização:\", formData.id);\n                        throw new Error(\"ID do agendamento inválido. Não é possível atualizar.\");\n                    }\n                    console.log(\"[SAVE] Atualizando agendamento com ID:\", formData.id);\n                    try {\n                        // Update existente\n                        await _app_modules_scheduler_services_appointmentService__WEBPACK_IMPORTED_MODULE_5__.appointmentService.updateAppointment(formData.id, {\n                            ...appointmentData,\n                            updatedBy: formData.providerId\n                        });\n                    } catch (updateError) {\n                        console.error(\"[SAVE] Erro ao atualizar agendamento:\", updateError);\n                        // Verificar se o erro é 404 (agendamento não encontrado)\n                        if (updateError.response && updateError.response.status === 404) {\n                            console.log(\"[SAVE] Agendamento não encontrado, tentando criar um novo\");\n                            // Tentar criar um novo agendamento com os mesmos dados\n                            await _app_modules_scheduler_services_appointmentService__WEBPACK_IMPORTED_MODULE_5__.appointmentService.createAppointment(appointmentData);\n                        } else {\n                            // Se não for 404, propagar o erro\n                            throw updateError;\n                        }\n                    }\n                } else {\n                    // Novo agendamento\n                    await _app_modules_scheduler_services_appointmentService__WEBPACK_IMPORTED_MODULE_5__.appointmentService.createAppointment(appointmentData);\n                }\n            }\n            // Exibir mensagem de sucesso\n            let successMessage = \"\";\n            if (isEditMode) {\n                successMessage = \"Agendamento atualizado com sucesso!\";\n            } else if (formData.recurrence.enabled) {\n                successMessage = \"Recorrência criada com sucesso!\";\n            } else {\n                successMessage = \"Agendamento criado com sucesso!\";\n            }\n            toast_success({\n                title: \"Sucesso\",\n                message: successMessage\n            });\n            onAppointmentChange();\n            onClose(); // Fechar o modal de agendamento apenas após sucesso\n        } catch (error) {\n            console.error(\"Erro ao salvar agendamento:\", error);\n            // Extrair mensagem de erro da resposta da API, se disponível\n            let errorMsg = \"Erro ao salvar o agendamento. Por favor, tente novamente.\";\n            if (error.response && error.response.data) {\n                if (error.response.data.message) {\n                    errorMsg = error.response.data.message;\n                    // Adicionar informações mais detalhadas para conflitos de horário\n                    if (error.response.data.reason === \"CONFLICT\" && error.response.data.conflictData) {\n                        const conflict = error.response.data.conflictData;\n                        const conflictStart = new Date(conflict.startDate);\n                        const conflictEnd = new Date(conflict.endDate);\n                        // Formatar as datas para exibição\n                        const formatDate = (date)=>{\n                            try {\n                                if (!date) return \"horário não especificado\";\n                                // Verificar se a data é válida\n                                if (isNaN(date.getTime())) return \"horário não disponível\";\n                                return date.toLocaleTimeString([], {\n                                    hour: '2-digit',\n                                    minute: '2-digit'\n                                });\n                            } catch (error) {\n                                console.error(\"Erro ao formatar data:\", error, date);\n                                return \"horário não disponível\";\n                            }\n                        };\n                        const appointmentTitle = conflict.title || \"Sem título\";\n                        errorMsg += ' Existe um agendamento conflitante \"'.concat(appointmentTitle, '\" no per\\xedodo ').concat(formatDate(conflictStart), \" - \").concat(formatDate(conflictEnd), \".\");\n                    }\n                } else if (error.response.data.error) {\n                    errorMsg = error.response.data.error;\n                }\n            } else if (error.message) {\n                errorMsg = error.message;\n            }\n            // Exibir toast de erro\n            toast_error({\n                title: \"Erro ao salvar agendamento\",\n                message: errorMsg\n            });\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    // Função para excluir agendamento após confirmação\n    const deleteAppointment = async ()=>{\n        // Verificar se o ID é válido antes de excluir\n        if (!formData.id || formData.id === \"undefined\") {\n            console.error(\"[DELETE] ID do agendamento inválido para exclusão:\", formData.id);\n            toast_error(\"ID do agendamento inválido. Não é possível excluir.\");\n            return;\n        }\n        setIsLoading(true);\n        console.log(\"[DELETE] Excluindo agendamento com ID:\", formData.id);\n        try {\n            try {\n                await _app_modules_scheduler_services_appointmentService__WEBPACK_IMPORTED_MODULE_5__.appointmentService.deleteAppointment(formData.id);\n            } catch (deleteError) {\n                console.error(\"[DELETE] Erro ao excluir agendamento:\", deleteError);\n                // Verificar se o erro é 404 (agendamento não encontrado)\n                if (deleteError.response && deleteError.response.status === 404) {\n                    // Se o agendamento não existe, considerar como excluído com sucesso\n                    console.log(\"[DELETE] Agendamento não encontrado, considerando como excluído\");\n                // Não propagar o erro, continuar como se tivesse excluído com sucesso\n                } else {\n                    // Se não for 404, propagar o erro\n                    throw deleteError;\n                }\n            }\n            // Exibir mensagem de sucesso\n            toast_success({\n                title: \"Sucesso\",\n                message: \"Agendamento excluído com sucesso!\"\n            });\n            onAppointmentChange();\n            onClose(); // Fechar o modal de agendamento apenas após sucesso\n        } catch (error) {\n            console.error(\"Erro ao excluir agendamento:\", error);\n            // Extrair mensagem de erro da resposta da API, se disponível\n            let errorMsg = \"Erro ao excluir o agendamento. Por favor, tente novamente.\";\n            if (error.response && error.response.data) {\n                if (error.response.data.message) {\n                    errorMsg = error.response.data.message;\n                } else if (error.response.data.error) {\n                    errorMsg = error.response.data.error;\n                }\n            } else if (error.message) {\n                errorMsg = error.message;\n            }\n            // Exibir toast de erro\n            toast_error({\n                title: \"Erro ao excluir agendamento\",\n                message: errorMsg\n            });\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        // Verificar novamente a permissão antes de enviar\n        if (!hasPermission || isReadOnlyMode) {\n            toast_error(isEditMode ? \"Você não tem permissão para editar agendamentos.\" : \"Você não tem permissão para criar agendamentos.\");\n            return;\n        }\n        // Validar dados do agendamento\n        const isValid = await validateAppointment();\n        if (!isValid) {\n            return;\n        }\n        // Preparar mensagem de confirmação\n        let actionMessage = \"\";\n        let actionTitle = \"\";\n        if (isEditMode) {\n            // Mensagem para edição de agendamento existente\n            actionTitle = \"Atualizar agendamento\";\n            actionMessage = 'Deseja atualizar o agendamento \"'.concat(formData.title, '\"?');\n        } else if (formData.recurrence.enabled) {\n            // Mensagem para criação de agendamento recorrente\n            actionTitle = \"Criar agendamentos recorrentes\";\n            actionMessage = \"Deseja criar uma s\\xe9rie de agendamentos recorrentes para \".concat(formData.title, \"?\");\n        } else {\n            // Mensagem para criação de agendamento normal ou sequencial\n            actionTitle = formData.sequentialAppointments > 1 ? \"Criar agendamentos sequenciais\" : \"Criar agendamento\";\n            actionMessage = formData.sequentialAppointments > 1 ? \"Deseja criar \".concat(formData.sequentialAppointments, ' agendamentos sequenciais para \"').concat(formData.title, '\"?') : 'Deseja criar o agendamento \"'.concat(formData.title, '\"?');\n        }\n        // Configurar ação para confirmação\n        setActionToConfirm({\n            type: \"save\",\n            message: actionMessage,\n            title: actionTitle,\n            variant: \"info\"\n        });\n        // Abrir diálogo de confirmação\n        setConfirmationDialogOpen(true);\n    };\n    // Função para solicitar confirmação de exclusão\n    const handleDelete = ()=>{\n        // Verificar permissão de exclusão\n        if (!canDelete) {\n            toast_error(\"Você não tem permissão para excluir agendamentos.\");\n            return;\n        }\n        if (!formData.id) return;\n        // Configurar ação para confirmação\n        setActionToConfirm({\n            type: \"delete\",\n            message: 'Tem certeza que deseja excluir o agendamento \"'.concat(formData.title, '\"?'),\n            title: \"Excluir agendamento\",\n            variant: \"danger\"\n        });\n        // Abrir diálogo de confirmação\n        setConfirmationDialogOpen(true);\n    };\n    // Função para calcular o horário do último agendamento sequencial\n    const calculateLastAppointmentTime = ()=>{\n        if (formData.sequentialAppointments <= 1 || !formData.startDate || !formData.endDate) {\n            return null;\n        }\n        // Converter strings ISO para objetos Date para cálculos\n        const endDate = new Date(formData.endDate);\n        // Duração fixa de 1 hora (3600000 ms) para agendamentos sequenciais\n        const sequentialDuration = 3600000; // 1 hora em milissegundos\n        // Começar com o horário de término do primeiro agendamento\n        let currentEndTime = endDate;\n        // Calcular o horário do último agendamento\n        for(let i = 1; i < formData.sequentialAppointments; i++){\n            // O próximo agendamento começa quando o anterior termina\n            const nextStartTime = new Date(currentEndTime.getTime());\n            // Duração fixa de 1 hora\n            const nextEndTime = new Date(nextStartTime.getTime() + sequentialDuration);\n            // Atualizar para o próximo agendamento\n            currentEndTime = nextEndTime;\n        }\n        // O último agendamento começa 1 hora antes do horário final calculado\n        const lastStartTime = new Date(currentEndTime.getTime() - sequentialDuration);\n        const lastEndTime = currentEndTime;\n        console.log(\"[SEQUENTIAL-CALC] \\xdaltimo agendamento: \".concat(lastStartTime.toLocaleString(), \" - \").concat(lastEndTime.toLocaleString()));\n        // Retornar os objetos Date diretamente\n        return {\n            start: lastStartTime,\n            end: lastEndTime\n        };\n    };\n    // Calcular o último horário para os agendamentos sequenciais\n    // Usar useEffect para recalcular sempre que formData mudar\n    const [lastAppointmentTime, setLastAppointmentTime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AppointmentModal.useEffect\": ()=>{\n            setLastAppointmentTime(calculateLastAppointmentTime());\n        }\n    }[\"AppointmentModal.useEffect\"], [\n        formData.sequentialAppointments,\n        formData.startDate,\n        formData.endDate\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ConfirmationDialog__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                isOpen: confirmationDialogOpen,\n                onClose: ()=>{\n                    console.log('[CONFIRMATION-DIALOG] Fechando diálogo de confirmação');\n                    // Fechar o diálogo de confirmação\n                    setConfirmationDialogOpen(false);\n                },\n                onConfirm: confirmAction,\n                title: (actionToConfirm === null || actionToConfirm === void 0 ? void 0 : actionToConfirm.title) || \"Confirmar ação\",\n                message: (actionToConfirm === null || actionToConfirm === void 0 ? void 0 : actionToConfirm.message) || \"\",\n                variant: (actionToConfirm === null || actionToConfirm === void 0 ? void 0 : actionToConfirm.variant) || \"info\",\n                confirmText: (actionToConfirm === null || actionToConfirm === void 0 ? void 0 : actionToConfirm.type) === \"delete\" ? \"Excluir\" : \"Confirmar\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\calendar\\\\AppointmentModal.js\",\n                lineNumber: 1280,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ModuleModal__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                isOpen: isOpen,\n                onClose: ()=>{\n                    // Sempre usar forceCloseModal para garantir o fechamento\n                    forceCloseModal();\n                },\n                animateExit: false,\n                title: selectedAppointment ? \"Editar Agendamento\" : \"Novo Agendamento\",\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                    size: 22\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\calendar\\\\AppointmentModal.js\",\n                    lineNumber: 1302,\n                    columnNumber: 15\n                }, void 0),\n                moduleColor: \"scheduler\",\n                size: \"xl\",\n                preventClose: false,\n                onInteractOutside: ()=>{\n                    console.log('[OUTSIDE-CLICK] Clique fora do modal');\n                    // Sempre permitir o fechamento do modal\n                    forceCloseModal();\n                },\n                footer: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_appointmentModal_ModuleModalFooter__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                    isEditMode: isEditMode,\n                    canDelete: canDelete && !isReadOnlyMode,\n                    hasPermission: hasPermission && !isReadOnlyMode,\n                    formData: formData,\n                    isLoading: isLoading,\n                    limitInfo: limitInfo,\n                    handleDelete: handleDelete,\n                    onClose: ()=>{\n                        console.log('[FOOTER-CLOSE] Tentativa de fechar o modal via footer');\n                        forceCloseModal(); // Forçar fechamento independente do estado\n                    }\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\calendar\\\\AppointmentModal.js\",\n                    lineNumber: 1312,\n                    columnNumber: 11\n                }, void 0),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                    id: \"appointment-form\",\n                    onSubmit: handleSubmit,\n                    className: \"overflow-y-auto dark:bg-gray-800 flex flex-col justify-between p-5\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-5\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_appointmentModal_BasicInfoForm__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                            formData: formData,\n                                            setFormData: setFormData,\n                                            readOnly: isReadOnlyMode\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\calendar\\\\AppointmentModal.js\",\n                                            lineNumber: 1335,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_appointmentModal_SpecialtySelector__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                            formData: formData,\n                                            setFormData: setFormData,\n                                            specialties: specialties,\n                                            readOnly: isReadOnlyMode\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\calendar\\\\AppointmentModal.js\",\n                                            lineNumber: 1342,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_appointmentModal_AppointmentSelectors__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            formData: formData,\n                                            setFormData: setFormData,\n                                            filteredProviders: filteredProviders,\n                                            persons: persons,\n                                            locations: locations,\n                                            serviceTypes: serviceTypes,\n                                            personInsurances: personInsurances,\n                                            isLoadingInsurances: isLoadingInsurances,\n                                            isLoading: isLoading,\n                                            readOnly: isReadOnlyMode\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\calendar\\\\AppointmentModal.js\",\n                                            lineNumber: 1350,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\calendar\\\\AppointmentModal.js\",\n                                    lineNumber: 1333,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-5\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_appointmentModal_AppointmentDates__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                            formData: formData,\n                                            setFormData: setFormData,\n                                            readOnly: isReadOnlyMode\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\calendar\\\\AppointmentModal.js\",\n                                            lineNumber: 1366,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        isEditMode && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_appointmentModal_AppointmentStatus__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                            formData: formData,\n                                            setFormData: setFormData,\n                                            readOnly: isReadOnlyMode\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\calendar\\\\AppointmentModal.js\",\n                                            lineNumber: 1374,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        !isEditMode && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mb-8\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_appointmentModal_SequentialAppointments__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                formData: formData,\n                                                setFormData: setFormData,\n                                                lastAppointmentTime: lastAppointmentTime\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\calendar\\\\AppointmentModal.js\",\n                                                lineNumber: 1384,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\calendar\\\\AppointmentModal.js\",\n                                            lineNumber: 1383,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        !isEditMode && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_appointmentModal_RecurrenceSettings__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                            formData: formData,\n                                            setFormData: setFormData\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\calendar\\\\AppointmentModal.js\",\n                                            lineNumber: 1394,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        formData.personId && formData.insuranceId && formData.serviceTypeId && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_InsuranceLimitsDisplay__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                            limitInfo: limitInfo,\n                                            isLoading: isCheckingLimits,\n                                            isEditMode: isEditMode\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\calendar\\\\AppointmentModal.js\",\n                                            lineNumber: 1402,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\calendar\\\\AppointmentModal.js\",\n                                    lineNumber: 1364,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\calendar\\\\AppointmentModal.js\",\n                            lineNumber: 1332,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\calendar\\\\AppointmentModal.js\",\n                        lineNumber: 1331,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\calendar\\\\AppointmentModal.js\",\n                    lineNumber: 1328,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\calendar\\\\AppointmentModal.js\",\n                lineNumber: 1294,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true);\n};\n_s(AppointmentModal, \"ZoEZAigQHwIJpzhwwu4gfJhoH9Y=\", false, function() {\n    return [\n        _contexts_ToastContext__WEBPACK_IMPORTED_MODULE_2__.useToast,\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__.useAuth\n    ];\n});\n_c = AppointmentModal;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (AppointmentModal);\nvar _c;\n$RefreshReg$(_c, \"AppointmentModal\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/calendar/AppointmentModal.js\n"));

/***/ })

});